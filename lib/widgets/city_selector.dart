import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class CitySelector extends StatelessWidget {
  final Function(String) onCitySelected;
  final String? selectedCity;
  final String hintText;
  final bool showIcon;
  final bool showBorder;
  final Color? backgroundColor;
  final EdgeInsetsGeometry padding;
  final BorderRadius? borderRadius;

  const CitySelector({
    Key? key,
    required this.onCitySelected,
    this.selectedCity,
    this.hintText = 'Select City',
    this.showIcon = true,
    this.showBorder = false,
    this.backgroundColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final locationProvider = Get.find<LocationProvider>();
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: () => _showCityBottomSheet(context, locationProvider),
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          color: backgroundColor ?? (isDarkMode ? const Color(0xFF2C2C2C) : AppTheme.backgroundLight),
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          border: showBorder
              ? Border.all(
                  color: isDarkMode ? Colors.white24 : AppTheme.borderColor,
                  width: 1,
                )
              : null,
        ),
        child: Row(
          children: [
            if (showIcon) ...[
              const Icon(
                Icons.location_city,
                color: AppTheme.textMedium,
              ),
              const SizedBox(width: 16),
            ],
            Obx(() {
              final city = selectedCity ?? locationProvider.city.value;
              return Text(
                city.isEmpty ? hintText : city,
                style: AppTheme.textTheme.bodyMedium?.copyWith(
                  color: city.isEmpty
                      ? (isDarkMode ? Colors.white30 : AppTheme.textLight)
                      : (isDarkMode ? Colors.white : AppTheme.textDark),
                ),
              );
            }),
            const Spacer(),
            const Icon(
              Icons.arrow_drop_down,
              color: AppTheme.textMedium,
            ),
          ],
        ),
      ),
    );
  }
  
  void _showCityBottomSheet(BuildContext context, LocationProvider locationProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Text(
                      'Select City',
                      style: AppTheme.textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: Obx(() {
                  if (locationProvider.isLoadingCities.value) {
                    return const Center(
                      child: LoadingIndicator(),
                    );
                  }
                  
                  final cities = locationProvider.cities;
                  
                  if (cities.isEmpty) {
                    return Center(
                      child: Text(
                        'No cities available',
                        style: AppTheme.textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textMedium,
                        ),
                      ),
                    );
                  }
                  
                  return ListView.builder(
                    controller: scrollController,
                    itemCount: cities.length,
                    itemBuilder: (context, index) {
                      final city = cities[index];
                      final cityName = city['city'];
                      final isSelected = selectedCity == cityName || locationProvider.city.value == cityName;
                      
                      return ListTile(
                        title: Text(cityName),
                        trailing: isSelected
                            ? const Icon(Icons.check, color: AppTheme.accentColor)
                            : null,
                        onTap: () {
                          onCitySelected(cityName);
                          Navigator.pop(context);
                        },
                      );
                    },
                  );
                }),
              ),
            ],
          );
        },
      ),
    );
  }
}

class CityChipSelector extends StatelessWidget {
  final Function(String) onCitySelected;
  final String? selectedCity;
  final EdgeInsetsGeometry padding;

  const CityChipSelector({
    Key? key,
    required this.onCitySelected,
    this.selectedCity,
    this.padding = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final locationProvider = Get.find<LocationProvider>();
    
    return Obx(() {
      if (locationProvider.isLoadingCities.value) {
        return const SizedBox(
          height: 50,
          child: Center(
            child: LoadingIndicator(size: 24),
          ),
        );
      }
      
      final cities = locationProvider.cities;
      
      if (cities.isEmpty) {
        return const SizedBox.shrink();
      }
      
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: padding,
        child: Row(
          children: cities.map((city) {
            final cityName = city['city'];
            final isSelected = selectedCity == cityName || locationProvider.city.value == cityName;
            
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: _buildCityChip(
                context,
                cityName,
                isSelected,
                () => onCitySelected(cityName),
              ),
            );
          }).toList(),
        ),
      );
    });
  }
  
  Widget _buildCityChip(
    BuildContext context,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.location_on,
              size: 16,
              color: isSelected ? Colors.white : AppTheme.textMedium,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: isSelected ? Colors.white : AppTheme.textMedium,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
