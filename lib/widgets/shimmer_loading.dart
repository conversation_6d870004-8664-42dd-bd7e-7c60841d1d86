import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class ShimmerLoading extends StatelessWidget {
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration period;
  final bool enabled;

  const ShimmerLoading({
    Key? key,
    required this.child,
    this.baseColor,
    this.highlightColor,
    this.period = const Duration(milliseconds: 1500),
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (!enabled) {
      return child;
    }
    
    return Shimmer.fromColors(
      baseColor: baseColor ?? (isDarkMode ? Colors.grey[800]! : Colors.grey[300]!),
      highlightColor: highlightColor ?? (isDarkMode ? Colors.grey[700]! : Colors.grey[100]!),
      period: period,
      child: child,
    );
  }
}

class ShimmerProductCard extends StatelessWidget {
  final bool isHorizontal;

  const ShimmerProductCard({
    Key? key,
    this.isHorizontal = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image placeholder
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              child: Container(
                width: double.infinity,
                height: isHorizontal ? 120 : 180,
                color: Colors.white,
              ),
            ),
            
            // Content placeholders
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title placeholder
                  Container(
                    width: double.infinity,
                    height: 16,
                    color: Colors.white,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Price placeholder
                  Container(
                    width: 100,
                    height: 14,
                    color: Colors.white,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Category placeholder
                  Container(
                    width: 80,
                    height: 12,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ShimmerProductGrid extends StatelessWidget {
  final int itemCount;
  final int crossAxisCount;

  const ShimmerProductGrid({
    Key? key,
    this.itemCount = 6,
    this.crossAxisCount = 2,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 0.7,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: itemCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return const ShimmerProductCard();
      },
    );
  }
}

class ShimmerListItem extends StatelessWidget {
  final double height;
  final double? width;
  final EdgeInsetsGeometry padding;
  final bool hasLeading;
  final bool hasTrailing;

  const ShimmerListItem({
    Key? key,
    this.height = 80,
    this.width,
    this.padding = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    this.hasLeading = true,
    this.hasTrailing = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Padding(
        padding: padding,
        child: Row(
          children: [
            // Leading circle
            if (hasLeading) ...[
              Container(
                width: 50,
                height: 50,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 16),
            ],
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: double.infinity,
                    height: 16,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 200,
                    height: 14,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
            
            // Trailing
            if (hasTrailing) ...[
              const SizedBox(width: 16),
              Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class ShimmerList extends StatelessWidget {
  final int itemCount;
  final bool scrollable;

  const ShimmerList({
    Key? key,
    this.itemCount = 5,
    this.scrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: itemCount,
      shrinkWrap: true,
      physics: scrollable ? null : const NeverScrollableScrollPhysics(),
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        return const ShimmerListItem();
      },
    );
  }
}

class ShimmerProfileHeader extends StatelessWidget {
  const ShimmerProfileHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile image
            Container(
              width: 100,
              height: 100,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Name
            Container(
              width: 150,
              height: 20,
              color: Colors.white,
            ),
            
            const SizedBox(height: 8),
            
            // Email
            Container(
              width: 200,
              height: 16,
              color: Colors.white,
            ),
          ],
        ),
      ),
    );
  }
}
