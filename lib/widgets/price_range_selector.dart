import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/utils/common.dart';

class PriceRangeSelector extends StatefulWidget {
  final double minValue;
  final double maxValue;
  final RangeValues initialValues;
  final Function(RangeValues) onChanged;
  final int divisions;
  final String? title;
  final bool showLabels;
  final bool showValues;
  final EdgeInsetsGeometry padding;

  const PriceRangeSelector({
    Key? key,
    required this.minValue,
    required this.maxValue,
    required this.initialValues,
    required this.onChanged,
    this.divisions = 100,
    this.title,
    this.showLabels = true,
    this.showValues = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
  }) : super(key: key);

  @override
  State<PriceRangeSelector> createState() => _PriceRangeSelectorState();
}

class _PriceRangeSelectorState extends State<PriceRangeSelector> {
  late RxDouble _minValue;
  late RxDouble _maxValue;

  @override
  void initState() {
    super.initState();
    _minValue = widget.initialValues.start.obs;
    _maxValue = widget.initialValues.end.obs;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: AppTheme.textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
          ],
          
          // Price range slider
          Obx(() {
            return Column(
              children: [
                if (widget.showValues)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          formatCurrency(_minValue.value.toInt()),
                          style: AppTheme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          formatCurrency(_maxValue.value.toInt()),
                          style: AppTheme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                
                SliderTheme(
                  data: SliderThemeData(
                    activeTrackColor: AppTheme.accentColor,
                    inactiveTrackColor: AppTheme.backgroundLight,
                    thumbColor: AppTheme.accentColor,
                    overlayColor: AppTheme.accentColor.withOpacity(0.2),
                    trackHeight: 4,
                    rangeThumbShape: const RoundRangeSliderThumbShape(
                      enabledThumbRadius: 10,
                      elevation: 2,
                    ),
                    rangeTrackShape: const RoundedRectRangeSliderTrackShape(),
                    rangeValueIndicatorShape: const PaddleRangeSliderValueIndicatorShape(),
                    valueIndicatorColor: AppTheme.accentColor,
                    valueIndicatorTextStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    showValueIndicator: ShowValueIndicator.always,
                  ),
                  child: RangeSlider(
                    values: RangeValues(_minValue.value, _maxValue.value),
                    min: widget.minValue,
                    max: widget.maxValue,
                    divisions: widget.divisions,
                    labels: RangeLabels(
                      formatCurrency(_minValue.value.toInt()),
                      formatCurrency(_maxValue.value.toInt()),
                    ),
                    onChanged: (values) {
                      _minValue.value = values.start;
                      _maxValue.value = values.end;
                      widget.onChanged(values);
                    },
                  ),
                ),
                
                if (widget.showLabels)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          formatCurrency(widget.minValue.toInt()),
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textLight,
                          ),
                        ),
                        Text(
                          formatCurrency(widget.maxValue.toInt()),
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }
}

class PriceRangeFilter extends StatelessWidget {
  final RangeValues priceRange;
  final Function(RangeValues) onChanged;
  final double minPrice;
  final double maxPrice;
  final String title;
  final bool expanded;
  final Function(bool)? onExpansionChanged;

  const PriceRangeFilter({
    Key? key,
    required this.priceRange,
    required this.onChanged,
    required this.minPrice,
    required this.maxPrice,
    this.title = 'Price Range',
    this.expanded = false,
    this.onExpansionChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: ExpansionTile(
        title: Text(
          title,
          style: AppTheme.textTheme.titleMedium,
        ),
        subtitle: Text(
          '${formatCurrency(priceRange.start.toInt())} - ${formatCurrency(priceRange.end.toInt())}',
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.textMedium,
          ),
        ),
        initiallyExpanded: expanded,
        onExpansionChanged: onExpansionChanged,
        children: [
          PriceRangeSelector(
            minValue: minPrice,
            maxValue: maxPrice,
            initialValues: priceRange,
            onChanged: onChanged,
            showLabels: true,
            showValues: true,
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          ),
        ],
      ),
    );
  }
}
