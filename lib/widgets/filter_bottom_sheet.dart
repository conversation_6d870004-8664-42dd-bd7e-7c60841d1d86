import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/category_selector.dart';
import 'package:Laradrobe/widgets/city_selector.dart';
import 'package:Laradrobe/widgets/custom_button.dart';
import 'package:Laradrobe/widgets/price_range_selector.dart';

class FilterBottomSheet extends StatefulWidget {
  final String? selectedCategory;
  final String? selectedCity;
  final RangeValues? priceRange;
  final Function(Map<String, dynamic>) onApplyFilters;
  final VoidCallback? onResetFilters;

  const FilterBottomSheet({
    Key? key,
    this.selectedCategory,
    this.selectedCity,
    this.priceRange,
    required this.onApplyFilters,
    this.onResetFilters,
  }) : super(key: key);

  static void show({
    required BuildContext context,
    String? selectedCategory,
    String? selectedCity,
    RangeValues? priceRange,
    required Function(Map<String, dynamic>) onApplyFilters,
    VoidCallback? onResetFilters,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return FilterBottomSheet(
            selectedCategory: selectedCategory,
            selectedCity: selectedCity,
            priceRange: priceRange,
            onApplyFilters: onApplyFilters,
            onResetFilters: onResetFilters,
          );
        },
      ),
    );
  }

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  final RxString _selectedCategory = ''.obs;
  final RxString _selectedCity = ''.obs;
  final Rx<RangeValues> _priceRange = Rx<RangeValues>(const RangeValues(0, 10000));
  
  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }
  
  void _initializeFilters() {
    // Initialize category
    if (widget.selectedCategory != null && widget.selectedCategory!.isNotEmpty) {
      _selectedCategory.value = widget.selectedCategory!;
    }
    
    // Initialize city
    if (widget.selectedCity != null && widget.selectedCity!.isNotEmpty) {
      _selectedCity.value = widget.selectedCity!;
    } else {
      final locationProvider = Get.find<LocationProvider>();
      if (locationProvider.city.value.isNotEmpty) {
        _selectedCity.value = locationProvider.city.value;
      }
    }
    
    // Initialize price range
    if (widget.priceRange != null) {
      _priceRange.value = widget.priceRange!;
    }
  }
  
  void _applyFilters() {
    final filters = <String, dynamic>{
      'category': _selectedCategory.value,
      'city': _selectedCity.value,
      'priceRange': _priceRange.value,
    };
    
    widget.onApplyFilters(filters);
    Get.back();
  }
  
  void _resetFilters() {
    _selectedCategory.value = '';
    _selectedCity.value = '';
    _priceRange.value = const RangeValues(0, 10000);
    
    if (widget.onResetFilters != null) {
      widget.onResetFilters!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Text(
                'Filter Products',
                style: AppTheme.textTheme.titleLarge,
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Get.back(),
              ),
            ],
          ),
        ),
        
        const Divider(),
        
        // Filter options
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category filter
                Text(
                  'Category',
                  style: AppTheme.textTheme.titleMedium,
                ),
                
                const SizedBox(height: 16),
                
                Obx(() => CategoryChipSelector(
                  selectedCategory: _selectedCategory.value,
                  onCategorySelected: (category) {
                    _selectedCategory.value = category;
                  },
                  padding: EdgeInsets.zero,
                )),
                
                const SizedBox(height: 24),
                
                // City filter
                Text(
                  'City',
                  style: AppTheme.textTheme.titleMedium,
                ),
                
                const SizedBox(height: 16),
                
                Obx(() => CitySelector(
                  selectedCity: _selectedCity.value,
                  onCitySelected: (city) {
                    _selectedCity.value = city;
                  },
                  showBorder: true,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                )),
                
                const SizedBox(height: 24),

                // Additional filters section (price filter removed for users)
                Text(
                  'Additional Filters',
                  style: AppTheme.textTheme.titleMedium,
                ),

                const SizedBox(height: 16),

                // Availability filter
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryLightColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppTheme.successColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Show only available items',
                        style: AppTheme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
        
        // Action buttons
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Row(
            children: [
              // Reset button
              Expanded(
                flex: 1,
                child: CustomButton(
                  text: 'Reset',
                  type: ButtonType.outline,
                  onPressed: _resetFilters,
                  isFullWidth: true,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Apply button
              Expanded(
                flex: 2,
                child: CustomButton(
                  text: 'Apply Filters',
                  type: ButtonType.primary,
                  onPressed: _applyFilters,
                  isFullWidth: true,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
