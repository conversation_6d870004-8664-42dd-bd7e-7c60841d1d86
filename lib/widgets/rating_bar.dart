import 'package:flutter/material.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class RatingBar extends StatelessWidget {
  final double rating;
  final double size;
  final int maxRating;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool showLabel;
  final TextStyle? labelStyle;
  final MainAxisAlignment mainAxisAlignment;
  final bool allowHalfRating;
  final EdgeInsetsGeometry padding;

  const RatingBar({
    Key? key,
    required this.rating,
    this.size = 20,
    this.maxRating = 5,
    this.activeColor,
    this.inactiveColor,
    this.showLabel = false,
    this.labelStyle,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.allowHalfRating = true,
    this.padding = EdgeInsets.zero,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Padding(
      padding: padding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: mainAxisAlignment,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(maxRating, (index) {
              final starPosition = index + 1;
              
              if (allowHalfRating) {
                if (starPosition <= rating) {
                  // Full star
                  return _buildStar(true, 1.0, isDarkMode);
                } else if (starPosition > rating && starPosition - 0.5 <= rating) {
                  // Half star
                  return _buildStar(true, 0.5, isDarkMode);
                } else {
                  // Empty star
                  return _buildStar(false, 0, isDarkMode);
                }
              } else {
                // No half rating, just full or empty stars
                return _buildStar(starPosition <= rating.round(), 1.0, isDarkMode);
              }
            }),
          ),
          
          if (showLabel) ...[
            const SizedBox(width: 4),
            Text(
              rating.toStringAsFixed(1),
              style: labelStyle ?? AppTheme.textTheme.bodySmall?.copyWith(
                color: isDarkMode ? Colors.white70 : AppTheme.textMedium,
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildStar(bool isActive, double fill, bool isDarkMode) {
    if (fill == 0) {
      // Empty star
      return Icon(
        Icons.star_border,
        size: size,
        color: inactiveColor ?? (isDarkMode ? Colors.white30 : AppTheme.textLight),
      );
    } else if (fill == 0.5) {
      // Half star
      return Icon(
        Icons.star_half,
        size: size,
        color: activeColor ?? Colors.amber,
      );
    } else {
      // Full star
      return Icon(
        Icons.star,
        size: size,
        color: activeColor ?? Colors.amber,
      );
    }
  }
}

class EditableRatingBar extends StatelessWidget {
  final double rating;
  final double size;
  final int maxRating;
  final Color? activeColor;
  final Color? inactiveColor;
  final Function(double) onRatingChanged;
  final bool allowHalfRating;
  final EdgeInsetsGeometry padding;

  const EditableRatingBar({
    Key? key,
    required this.rating,
    required this.onRatingChanged,
    this.size = 30,
    this.maxRating = 5,
    this.activeColor,
    this.inactiveColor,
    this.allowHalfRating = true,
    this.padding = EdgeInsets.zero,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Padding(
      padding: padding,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(maxRating, (index) {
          final starPosition = index + 1;
          
          return GestureDetector(
            onTap: () => onRatingChanged(starPosition.toDouble()),
            onHorizontalDragUpdate: allowHalfRating
                ? (details) {
                    final box = context.findRenderObject() as RenderBox;
                    final pos = box.globalToLocal(details.globalPosition);
                    final i = pos.dx / size;
                    
                    // Calculate the rating based on the position
                    double newRating;
                    if (i <= 0) {
                      newRating = 0;
                    } else {
                      newRating = allowHalfRating
                          ? (i.round() * 2).ceilToDouble() / 2
                          : i.round().toDouble();
                    }
                    
                    // Ensure the rating is within bounds
                    newRating = newRating.clamp(0, maxRating.toDouble());
                    
                    onRatingChanged(newRating);
                  }
                : null,
            child: _buildStar(
              starPosition <= rating,
              allowHalfRating && starPosition > rating && starPosition - 0.5 <= rating ? 0.5 : 1.0,
              isDarkMode,
            ),
          );
        }),
      ),
    );
  }
  
  Widget _buildStar(bool isActive, double fill, bool isDarkMode) {
    if (!isActive) {
      // Empty star
      return Icon(
        Icons.star_border,
        size: size,
        color: inactiveColor ?? (isDarkMode ? Colors.white30 : AppTheme.textLight),
      );
    } else if (fill == 0.5) {
      // Half star
      return Icon(
        Icons.star_half,
        size: size,
        color: activeColor ?? Colors.amber,
      );
    } else {
      // Full star
      return Icon(
        Icons.star,
        size: size,
        color: activeColor ?? Colors.amber,
      );
    }
  }
}
