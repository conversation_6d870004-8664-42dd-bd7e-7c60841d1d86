import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class CustomNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? errorWidget;
  final Widget? loadingWidget;
  final Color? backgroundColor;
  final bool showLoading;

  const CustomNetworkImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.errorWidget,
    this.loadingWidget,
    this.backgroundColor,
    this.showLoading = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (imageUrl.isEmpty) {
      return _buildErrorWidget(isDarkMode);
    }
    
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => _buildLoadingWidget(isDarkMode),
        errorWidget: (context, url, error) => _buildErrorWidget(isDarkMode),
      ),
    );
  }
  
  Widget _buildLoadingWidget(bool isDarkMode) {
    if (!showLoading) {
      return Container(
        width: width,
        height: height,
        color: backgroundColor ?? (isDarkMode ? AppTheme.backgroundLight.withOpacity(0.5) : AppTheme.backgroundLight),
      );
    }
    
    return loadingWidget ?? Container(
      width: width,
      height: height,
      color: backgroundColor ?? (isDarkMode ? AppTheme.backgroundLight.withOpacity(0.5) : AppTheme.backgroundLight),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }
  
  Widget _buildErrorWidget(bool isDarkMode) {
    return errorWidget ?? Container(
      width: width,
      height: height,
      color: backgroundColor ?? (isDarkMode ? AppTheme.backgroundLight.withOpacity(0.5) : AppTheme.backgroundLight),
      child: Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          color: isDarkMode ? Colors.white54 : AppTheme.textLight,
          size: (width != null && height != null) ? (width! < height! ? width! / 3 : height! / 3) : 40,
        ),
      ),
    );
  }
}

class CircleNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double size;
  final Widget? errorWidget;
  final Widget? loadingWidget;
  final Color? backgroundColor;
  final bool showLoading;
  final BoxBorder? border;

  const CircleNetworkImage({
    Key? key,
    required this.imageUrl,
    this.size = 50,
    this.errorWidget,
    this.loadingWidget,
    this.backgroundColor,
    this.showLoading = true,
    this.border,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (imageUrl.isEmpty) {
      return _buildErrorWidget(isDarkMode);
    }
    
    return ClipOval(
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildLoadingWidget(isDarkMode),
        errorWidget: (context, url, error) => _buildErrorWidget(isDarkMode),
      ),
    );
  }
  
  Widget _buildLoadingWidget(bool isDarkMode) {
    if (!showLoading) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: backgroundColor ?? (isDarkMode ? AppTheme.backgroundLight.withOpacity(0.5) : AppTheme.backgroundLight),
          border: border,
        ),
      );
    }
    
    return loadingWidget ?? Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? (isDarkMode ? AppTheme.backgroundLight.withOpacity(0.5) : AppTheme.backgroundLight),
        border: border,
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }
  
  Widget _buildErrorWidget(bool isDarkMode) {
    return errorWidget ?? Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? (isDarkMode ? AppTheme.backgroundLight.withOpacity(0.5) : AppTheme.backgroundLight),
        border: border,
      ),
      child: Center(
        child: Icon(
          Icons.person,
          color: isDarkMode ? Colors.white54 : AppTheme.textLight,
          size: size / 2,
        ),
      ),
    );
  }
}

class ProductNetworkImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final bool isNew;
  final bool isBest;
  final bool showBadge;

  const ProductNetworkImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.isNew = false,
    this.isBest = false,
    this.showBadge = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CustomNetworkImage(
          imageUrl: imageUrl,
          width: width,
          height: height,
          fit: fit,
          borderRadius: borderRadius,
        ),
        
        if (showBadge && (isNew || isBest))
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: isNew ? AppTheme.accentColor : Colors.amber,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isBest) ...[
                    const Icon(
                      Icons.star,
                      color: Colors.white,
                      size: 12,
                    ),
                    const SizedBox(width: 2),
                  ],
                  Text(
                    isNew ? 'NEW' : 'BEST',
                    style: AppTheme.textTheme.labelSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
