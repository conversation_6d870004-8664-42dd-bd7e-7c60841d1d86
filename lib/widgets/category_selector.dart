import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class CategorySelector extends StatelessWidget {
  final Function(String) onCategorySelected;
  final bool showAllOption;
  final bool showTitle;
  final String? selectedCategory;
  final double height;
  final EdgeInsetsGeometry padding;

  const CategorySelector({
    Key? key,
    required this.onCategorySelected,
    this.showAllOption = true,
    this.showTitle = true,
    this.selectedCategory,
    this.height = 120,
    this.padding = const EdgeInsets.symmetric(vertical: 8),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Get.find<CategoryProvider>();
    
    return Obx(() {
      if (categoryProvider.isLoading.value) {
        return SizedBox(
          height: height,
          child: const Center(
            child: LoadingIndicator(size: 24),
          ),
        );
      }
      
      return Container(
        height: height,
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Text(
                      'Categories',
                      style: AppTheme.textTheme.titleMedium,
                    ),
                    const Spacer(),
                    if (showAllOption)
                      TextButton(
                        onPressed: () => onCategorySelected(''),
                        child: Text(
                          'View All',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.accentColor,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: categoryProvider.categories.length,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                itemBuilder: (context, index) {
                  final category = categoryProvider.categories[index];
                  final isSelected = selectedCategory == category['name'] ||
                      (selectedCategory == null && categoryProvider.selectedCategory.value == category['name']);
                  
                  return GestureDetector(
                    onTap: () => onCategorySelected(category['name']),
                    child: Container(
                      width: 80,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Category image
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected ? AppTheme.primaryColor : AppTheme.backgroundLight,
                              border: isSelected
                                  ? Border.all(color: AppTheme.accentColor, width: 2)
                                  : null,
                            ),
                            child: Center(
                              child: Image.asset(
                                'assets/images/${category['image']}',
                                width: 40,
                                height: 40,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // Category name
                          Text(
                            category['name'],
                            style: AppTheme.textTheme.bodySmall?.copyWith(
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              color: isSelected ? AppTheme.accentColor : AppTheme.textMedium,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}

class CategoryChipSelector extends StatelessWidget {
  final Function(String) onCategorySelected;
  final bool showAllOption;
  final String? selectedCategory;
  final EdgeInsetsGeometry padding;

  const CategoryChipSelector({
    Key? key,
    required this.onCategorySelected,
    this.showAllOption = true,
    this.selectedCategory,
    this.padding = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Get.find<CategoryProvider>();
    
    return Obx(() {
      if (categoryProvider.isLoading.value) {
        return const SizedBox(
          height: 50,
          child: Center(
            child: LoadingIndicator(size: 24),
          ),
        );
      }
      
      return Padding(
        padding: padding,
        child: Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            if (showAllOption)
              _buildCategoryChip(
                context,
                'All',
                selectedCategory == null || selectedCategory!.isEmpty,
                () => onCategorySelected(''),
              ),
            
            ...categoryProvider.categories.map((category) {
              final isSelected = selectedCategory == category['name'] ||
                  (selectedCategory == null && categoryProvider.selectedCategory.value == category['name']);
              
              return _buildCategoryChip(
                context,
                category['name'],
                isSelected,
                () => onCategorySelected(category['name']),
              );
            }).toList(),
          ],
        ),
      );
    });
  }
  
  Widget _buildCategoryChip(
    BuildContext context,
    String label,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.accentColor : AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: isSelected ? Colors.white : AppTheme.textMedium,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}

class CategoryGridSelector extends StatelessWidget {
  final Function(String) onCategorySelected;
  final bool showAllOption;
  final String? selectedCategory;
  final int crossAxisCount;
  final EdgeInsetsGeometry padding;

  const CategoryGridSelector({
    Key? key,
    required this.onCategorySelected,
    this.showAllOption = true,
    this.selectedCategory,
    this.crossAxisCount = 3,
    this.padding = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Get.find<CategoryProvider>();
    
    return Obx(() {
      if (categoryProvider.isLoading.value) {
        return const SizedBox(
          height: 200,
          child: Center(
            child: LoadingIndicator(size: 24),
          ),
        );
      }
      
      final categories = categoryProvider.categories;
      
      return Padding(
        padding: padding,
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: 1,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: categories.length + (showAllOption ? 1 : 0),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            if (showAllOption && index == 0) {
              return _buildCategoryItem(
                context,
                'All',
                'all_categories.png',
                selectedCategory == null || selectedCategory!.isEmpty,
                () => onCategorySelected(''),
              );
            }
            
            final actualIndex = showAllOption ? index - 1 : index;
            final category = categories[actualIndex];
            final isSelected = selectedCategory == category['name'] ||
                (selectedCategory == null && categoryProvider.selectedCategory.value == category['name']);
            
            return _buildCategoryItem(
              context,
              category['name'],
              category['image'],
              isSelected,
              () => onCategorySelected(category['name']),
            );
          },
        ),
      );
    });
  }
  
  Widget _buildCategoryItem(
    BuildContext context,
    String label,
    String image,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryLightColor : AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(color: AppTheme.accentColor, width: 2)
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/$image',
              width: 60,
              height: 60,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? AppTheme.accentColor : AppTheme.textMedium,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
