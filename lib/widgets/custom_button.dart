import 'package:flutter/material.dart';
import 'package:<PERSON><PERSON><PERSON>/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

enum ButtonType {
  primary,
  secondary,
  outline,
  text,
  error,
  success,
}

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final bool isLoading;
  final bool isFullWidth;
  final double? width;
  final double height;
  final EdgeInsetsGeometry padding;
  final BorderRadius? borderRadius;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.isLoading = false,
    this.isFullWidth = false,
    this.width,
    this.height = 48,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
    this.borderRadius,
    this.prefixIcon,
    this.suffixIcon,
    this.textStyle,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    // Determine button style based on type
    ButtonStyle getButtonStyle() {
      switch (type) {
        case ButtonType.primary:
          return ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppTheme.accentColor,
            foregroundColor: foregroundColor ?? Colors.white,
            elevation: elevation,
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
            disabledBackgroundColor: AppTheme.textLight,
            disabledForegroundColor: Colors.white,
          );
        case ButtonType.secondary:
          return ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppTheme.primaryColor,
            foregroundColor: foregroundColor ?? AppTheme.textDark,
            elevation: elevation,
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
            disabledBackgroundColor: AppTheme.textLight,
            disabledForegroundColor: Colors.white,
          );
        case ButtonType.outline:
          return OutlinedButton.styleFrom(
            foregroundColor: foregroundColor ?? (isDarkMode ? AppTheme.primaryColor : AppTheme.accentColor),
            side: BorderSide(
              color: backgroundColor ?? (isDarkMode ? AppTheme.primaryColor : AppTheme.accentColor),
              width: 1.5,
            ),
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
          );
        case ButtonType.text:
          return TextButton.styleFrom(
            foregroundColor: foregroundColor ?? (isDarkMode ? AppTheme.primaryColor : AppTheme.accentColor),
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
          );
        case ButtonType.error:
          return ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppTheme.errorColor,
            foregroundColor: foregroundColor ?? Colors.white,
            elevation: elevation,
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
            disabledBackgroundColor: AppTheme.textLight,
            disabledForegroundColor: Colors.white,
          );
        case ButtonType.success:
          return ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppTheme.successColor,
            foregroundColor: foregroundColor ?? Colors.white,
            elevation: elevation,
            padding: padding,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8),
            ),
            disabledBackgroundColor: AppTheme.textLight,
            disabledForegroundColor: Colors.white,
          );
      }
    }
    
    // Build button content
    Widget buttonContent() {
      if (isLoading) {
        return SizedBox(
          height: 24,
          width: 24,
          child: LoadingIndicator(
            color: _getLoadingColor(),
            size: 24,
          ),
        );
      }
      
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (prefixIcon != null) ...[
            prefixIcon!,
            const SizedBox(width: 8),
          ],
          Text(
            text,
            style: textStyle ?? AppTheme.textTheme.labelLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          if (suffixIcon != null) ...[
            const SizedBox(width: 8),
            suffixIcon!,
          ],
        ],
      );
    }
    
    // Build the button based on type
    Widget buildButton() {
      switch (type) {
        case ButtonType.primary:
        case ButtonType.secondary:
        case ButtonType.error:
        case ButtonType.success:
          return ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: getButtonStyle(),
            child: buttonContent(),
          );
        case ButtonType.outline:
          return OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: getButtonStyle(),
            child: buttonContent(),
          );
        case ButtonType.text:
          return TextButton(
            onPressed: isLoading ? null : onPressed,
            style: getButtonStyle(),
            child: buttonContent(),
          );
      }
    }
    
    // Apply width constraints
    if (isFullWidth) {
      return SizedBox(
        width: double.infinity,
        height: height,
        child: buildButton(),
      );
    } else if (width != null) {
      return SizedBox(
        width: width,
        height: height,
        child: buildButton(),
      );
    } else {
      return SizedBox(
        height: height,
        child: buildButton(),
      );
    }
  }
  
  Color _getLoadingColor() {
    switch (type) {
      case ButtonType.primary:
      case ButtonType.error:
      case ButtonType.success:
        return Colors.white;
      case ButtonType.secondary:
        return AppTheme.textDark;
      case ButtonType.outline:
      case ButtonType.text:
        return AppTheme.accentColor;
    }
  }
}
