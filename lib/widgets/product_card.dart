import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/env.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/bottom_navigation_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class ProductCard extends StatelessWidget {
  final Map<String, dynamic> product;
  final Function(String) onTap;
  final bool isHorizontal;

  const ProductCard({
    Key? key,
    required this.product,
    required this.onTap,
    this.isHorizontal = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    final homeProvider = Get.find<HomeProvider>();
    final bottomNavProvider = Get.find<BottomNavigationProvider>();
    
    final imageUrl = '${dotenv.env[Env.productImageUrl]}${product["thumbnail"]}';
    
    return GestureDetector(
      onTap: () => onTap(product['id']),
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardTheme.color,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              child: Stack(
                children: [
                  // Product image
                  AspectRatio(
                    aspectRatio: 1,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppTheme.backgroundLight,
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: AppTheme.backgroundLight,
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported_outlined,
                            color: AppTheme.textLight,
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Favorite button
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.4),
                        shape: BoxShape.circle,
                      ),
                      child: Obx(() {
                        if (!authProvider.isAuthenticated.value) {
                          return IconButton(
                            onPressed: () => bottomNavProvider.changeTabIndex(4),
                            icon: const Icon(
                              Icons.favorite_border_outlined,
                              color: Colors.white,
                              size: 18,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          );
                        }
                        
                        final isInCart = product['is_in_cart'] != null && product['is_in_cart'] > 0;
                        
                        return IconButton(
                          onPressed: () => homeProvider.toggleFavorite(
                            product['id'],
                            category: product['category'],
                          ),
                          icon: Icon(
                            isInCart ? Icons.favorite : Icons.favorite_border,
                            color: Colors.white,
                            size: 18,
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
            
            // Product details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    product['name'],
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTheme.textTheme.titleMedium,
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // Rating and availability (price hidden for users)
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${product['rating'] ?? 4.5}',
                        style: AppTheme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Available',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.successColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),
                  
                  // Category
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryLightColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      product['category'],
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
