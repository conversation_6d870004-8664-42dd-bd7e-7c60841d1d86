import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final bool showBackButton;
  final double elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final PreferredSizeWidget? bottom;
  final VoidCallback? onBackPressed;
  final Widget? flexibleSpace;
  final double? toolbarHeight;
  final bool automaticallyImplyLeading;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.showBackButton = true,
    this.elevation = 0,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
    this.onBackPressed,
    this.flexibleSpace,
    this.toolbarHeight,
    this.automaticallyImplyLeading = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AppBar(
      title: Text(
        title,
        style: AppTheme.textTheme.titleLarge?.copyWith(
          color: foregroundColor ?? (isDarkMode ? Colors.white : AppTheme.textDark),
        ),
      ),
      centerTitle: centerTitle,
      elevation: elevation,
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      foregroundColor: foregroundColor ?? (isDarkMode ? Colors.white : AppTheme.textDark),
      actions: actions,
      leading: _buildLeading(context, isDarkMode),
      bottom: bottom,
      flexibleSpace: flexibleSpace,
      toolbarHeight: toolbarHeight,
      automaticallyImplyLeading: automaticallyImplyLeading,
    );
  }
  
  Widget? _buildLeading(BuildContext context, bool isDarkMode) {
    if (leading != null) {
      return leading;
    }
    
    if (showBackButton && Navigator.of(context).canPop()) {
      return IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: onBackPressed ?? () => Get.back(),
        tooltip: MaterialLocalizations.of(context).backButtonTooltip,
        color: foregroundColor ?? (isDarkMode ? Colors.white : AppTheme.textDark),
      );
    }
    
    return null;
  }
  
  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight + (bottom?.preferredSize.height ?? 0.0));
}

class SearchAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String hintText;
  final TextEditingController? controller;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onClear;
  final List<Widget>? actions;
  final bool showBackButton;
  final double elevation;
  final Color? backgroundColor;
  final PreferredSizeWidget? bottom;
  final VoidCallback? onBackPressed;

  const SearchAppBar({
    Key? key,
    this.hintText = 'Search',
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.actions,
    this.showBackButton = true,
    this.elevation = 0,
    this.backgroundColor,
    this.bottom,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AppBar(
      titleSpacing: 0,
      title: TextField(
        controller: controller,
        onChanged: onChanged,
        onSubmitted: onSubmitted,
        decoration: InputDecoration(
          hintText: hintText,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          prefixIcon: const Icon(Icons.search),
          suffixIcon: controller != null && controller!.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    controller!.clear();
                    if (onClear != null) {
                      onClear!();
                    }
                  },
                )
              : null,
        ),
        textInputAction: TextInputAction.search,
      ),
      elevation: elevation,
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      leading: showBackButton && Navigator.of(context).canPop()
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => Get.back(),
              tooltip: MaterialLocalizations.of(context).backButtonTooltip,
              color: isDarkMode ? Colors.white : AppTheme.textDark,
            )
          : null,
      actions: actions,
      bottom: bottom,
    );
  }
  
  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + (bottom?.preferredSize.height ?? 0.0));
}

class TransparentAppBar extends StatelessWidget implements PreferredSizeWidget {
  final List<Widget>? actions;
  final Color? iconColor;
  final double elevation;
  final VoidCallback? onBackPressed;
  final bool showBackButton;

  const TransparentAppBar({
    Key? key,
    this.actions,
    this.iconColor = Colors.white,
    this.elevation = 0,
    this.onBackPressed,
    this.showBackButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: elevation,
      leading: showBackButton
          ? IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.arrow_back,
                  color: iconColor,
                  size: 20,
                ),
              ),
              onPressed: onBackPressed ?? () => Get.back(),
            )
          : null,
      actions: actions != null
          ? actions!.map((action) {
              if (action is IconButton) {
                return IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.4),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      (action.icon as Icon).icon,
                      color: iconColor,
                      size: 20,
                    ),
                  ),
                  onPressed: action.onPressed,
                );
              }
              return action;
            }).toList()
          : null,
    );
  }
  
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
