import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/models/referral.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/utils/common.dart';
import 'package:Laradrobe/widgets/custom_app_bar.dart';
import 'package:Laradrobe/widgets/custom_button.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:Laradrobe/widgets/network_image.dart';

class ReferralScreen extends StatefulWidget {
  const ReferralScreen({Key? key}) : super(key: key);

  @override
  State<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends State<ReferralScreen> {
  final authProvider = Get.find<AuthProvider>();
  final RxList<Referral> _referrals = <Referral>[].obs;
  final RxBool _isLoading = false.obs;
  final RxInt _totalEarnings = 0.obs;
  
  @override
  void initState() {
    super.initState();
    _fetchReferrals();
  }
  
  Future<void> _fetchReferrals() async {
    _isLoading.value = true;
    
    try {
      final referrals = await ApiService.getReferredUsers();
      _referrals.value = referrals;
      
      // Calculate total earnings
      _totalEarnings.value = referrals.fold(0, (sum, referral) => sum + (referral.reward ?? 0));
    } catch (e) {
      print('Error fetching referrals: $e');
      Get.snackbar(
        'Error',
        'Failed to load referrals. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.error_outline,
          color: Colors.white,
        ),
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  void _shareReferralCode() {
    // TODO: Implement share functionality
    Get.snackbar(
      'Coming Soon',
      'Share functionality will be available soon!',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppTheme.infoColor,
      colorText: Colors.white,
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      icon: const Icon(
        Icons.share,
        color: Colors.white,
      ),
    );
  }
  
  void _copyReferralCode() {
    final referralCode = authProvider.user['referral_code'] ?? '';
    if (referralCode.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: referralCode));
      Get.snackbar(
        'Copied',
        'Referral code copied to clipboard',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'My Referrals',
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }
        
        return Column(
          children: [
            // Referral card
            _buildReferralCard(),
            
            // Referral list
            Expanded(
              child: _referrals.isEmpty
                  ? const EmptyState(
                      icon: Icons.people_outline,
                      title: 'No Referrals Yet',
                      message: 'You haven\'t referred anyone yet. Share your referral code to earn rewards!',
                    )
                  : RefreshIndicator(
                      onRefresh: _fetchReferrals,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _referrals.length,
                        itemBuilder: (context, index) {
                          final referral = _referrals[index];
                          return _buildReferralItem(referral);
                        },
                      ),
                    ),
            ),
          ],
        );
      }),
    );
  }
  
  Widget _buildReferralCard() {
    final referralCode = authProvider.user['referral_code'] ?? '';
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Earnings
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Total Earnings',
                        style: AppTheme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Obx(() => Text(
                        formatCurrency(_totalEarnings.value),
                        style: AppTheme.textTheme.headlineSmall?.copyWith(
                          color: AppTheme.accentColor,
                          fontWeight: FontWeight.bold,
                        ),
                      )),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Referrals',
                        style: AppTheme.textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Obx(() => Text(
                        '${_referrals.length}',
                        style: AppTheme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      )),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            
            // Referral code
            Text(
              'Your Referral Code',
              style: AppTheme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: AppTheme.backgroundLight,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.borderColor,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Text(
                    referralCode.isEmpty ? 'No referral code available' : referralCode,
                    style: AppTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.2,
                    ),
                  ),
                  const Spacer(),
                  if (referralCode.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: _copyReferralCode,
                      tooltip: 'Copy code',
                    ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Share button
            CustomButton(
              text: 'Share Referral Code',
              type: ButtonType.primary,
              isFullWidth: true,
              prefixIcon: const Icon(Icons.share, size: 18),
              onPressed: referralCode.isNotEmpty ? _shareReferralCode : null,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildReferralItem(Referral referral) {
    final user = referral.referredUser;
    final date = formatDate(referral.createdAt);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // User avatar
            CircleNetworkImage(
              imageUrl: user?.profileImage ?? '',
              size: 50,
              backgroundColor: AppTheme.backgroundLight,
            ),
            
            const SizedBox(width: 16),
            
            // User details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user?.name ?? 'Unknown User',
                    style: AppTheme.textTheme.titleMedium,
                  ),
                  Text(
                    'Joined $date',
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textLight,
                    ),
                  ),
                ],
              ),
            ),
            
            // Reward
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  formatCurrency(referral.reward ?? 0),
                  style: AppTheme.textTheme.titleMedium?.copyWith(
                    color: AppTheme.accentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(referral.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    referral.status.capitalize ?? 'Unknown',
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(referral.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successColor;
      case 'pending':
        return Colors.amber;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return AppTheme.textLight;
    }
  }
}
