import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({Key? key}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final authProvider = Get.find<AuthProvider>();
  final locationProvider = Get.find<LocationProvider>();
  
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  
  final RxString _selectedCity = ''.obs;
  final RxBool _isLoading = false.obs;
  final Rx<File?> _profileImage = Rx<File?>(null);
  
  @override
  void initState() {
    super.initState();
    _initializeForm();
  }
  
  void _initializeForm() {
    final user = authProvider.user;
    
    _nameController.text = user['name'] ?? '';
    
    if (user['phone'] != null) {
      _phoneController.text = user['phone'].toString();
    }
    
    if (user['address'] != null) {
      _addressController.text = user['address'];
    }
    
    if (user['city'] != null) {
      _selectedCity.value = user['city'];
    }
  }
  
  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    
    if (image != null) {
      _profileImage.value = File(image.path);
    }
  }
  
  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    _isLoading.value = true;
    
    try {
      final profileData = {
        'name': _nameController.text,
        'phone': _phoneController.text,
        'address': _addressController.text,
        'city': _selectedCity.value,
      };
      
      // TODO: Handle profile image upload
      
      await ApiService.updateProfile(profileData);
      
      // Refresh user data
      await authProvider.checkAuthStatus();
      
      Get.back();
      Get.snackbar(
        'Success',
        'Profile updated successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error updating profile: $e');
      Get.snackbar(
        'Error',
        'Failed to update profile. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  void _openCitySelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildCitySelector(),
    );
  }
  
  Widget _buildCitySelector() {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) {
        return Obx(() {
          final cities = locationProvider.cities;
          
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Text(
                      'Select City',
                      style: AppTheme.textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: locationProvider.isLoadingCities.value
                    ? const LoadingIndicator()
                    : ListView.builder(
                        controller: scrollController,
                        itemCount: cities.length,
                        itemBuilder: (context, index) {
                          final city = cities[index];
                          return ListTile(
                            title: Text(city['city']),
                            trailing: _selectedCity.value == city['city']
                                ? const Icon(Icons.check, color: AppTheme.accentColor)
                                : null,
                            onTap: () {
                              _selectedCity.value = city['city'];
                              Navigator.pop(context);
                            },
                          );
                        },
                      ),
              ),
            ],
          );
        });
      },
    );
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile image
                Center(
                  child: Stack(
                    children: [
                      // Profile image
                      Obx(() {
                        final user = authProvider.user;
                        
                        return Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppTheme.backgroundLight,
                            image: _profileImage.value != null
                                ? DecorationImage(
                                    image: FileImage(_profileImage.value!),
                                    fit: BoxFit.cover,
                                  )
                                : user['profile_image'] != null
                                    ? DecorationImage(
                                        image: NetworkImage(user['profile_image']),
                                        fit: BoxFit.cover,
                                      )
                                    : null,
                          ),
                          child: _profileImage.value == null && user['profile_image'] == null
                              ? const Icon(
                                  Icons.person,
                                  size: 60,
                                  color: AppTheme.textMedium,
                                )
                              : null,
                        );
                      }),
                      
                      // Edit button
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: _pickImage,
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppTheme.accentColor,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Name field
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Name',
                    prefixIcon: Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Phone field
                TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Phone',
                    prefixIcon: Icon(Icons.phone),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                
                const SizedBox(height: 16),
                
                // Address field
                TextFormField(
                  controller: _addressController,
                  decoration: const InputDecoration(
                    labelText: 'Address',
                    prefixIcon: Icon(Icons.home),
                  ),
                  maxLines: 2,
                ),
                
                const SizedBox(height: 16),
                
                // City field
                GestureDetector(
                  onTap: _openCitySelector,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.location_city,
                          color: AppTheme.textMedium,
                        ),
                        const SizedBox(width: 16),
                        Obx(() {
                          return Text(
                            _selectedCity.value.isEmpty
                                ? 'Select City'
                                : _selectedCity.value,
                            style: AppTheme.textTheme.bodyMedium?.copyWith(
                              color: _selectedCity.value.isEmpty
                                  ? AppTheme.textLight
                                  : AppTheme.textDark,
                            ),
                          );
                        }),
                        const Spacer(),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: AppTheme.textMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Save button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _saveProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Save Profile'),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
