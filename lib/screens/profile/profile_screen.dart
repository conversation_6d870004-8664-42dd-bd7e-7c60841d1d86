import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/theme_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    final themeProvider = Get.find<ThemeProvider>();
    
    return Scaffold(
      body: SafeArea(
        child: Obx(() {
          if (authProvider.isLoading.value) {
            return const Center(
              child: LoadingIndicator(),
            );
          }
          
          if (!authProvider.isAuthenticated.value) {
            return _buildNotLoggedInView();
          }
          
          final user = authProvider.user;
          
          return CustomScrollView(
            slivers: [
              // App bar
              SliverAppBar(
                pinned: true,
                expandedHeight: 200,
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                automaticallyImplyLeading: false,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.primaryLightColor,
                        ],
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Profile image
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                              image: user['profile_image'] != null
                                  ? DecorationImage(
                                      image: NetworkImage(user['profile_image']),
                                      fit: BoxFit.cover,
                                    )
                                  : null,
                            ),
                            child: user['profile_image'] == null
                                ? const Icon(
                                    Icons.person,
                                    size: 40,
                                    color: AppTheme.textMedium,
                                  )
                                : null,
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // User name
                          Text(
                            user['name'] ?? 'User',
                            style: AppTheme.textTheme.titleLarge?.copyWith(
                              color: AppTheme.textDark,
                            ),
                          ),
                          
                          // User email
                          if (user['email'] != null)
                            Text(
                              user['email'],
                              style: AppTheme.textTheme.bodyMedium?.copyWith(
                                color: AppTheme.textMedium,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                actions: [
                  // Edit profile button
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => Get.toNamed(Routes.editProfile),
                  ),
                  
                  // Settings button
                  IconButton(
                    icon: const Icon(Icons.settings),
                    onPressed: () {
                      // TODO: Navigate to settings
                      Get.snackbar(
                        'Coming Soon',
                        'Settings will be available soon!',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                  ),
                ],
              ),
              
              // Profile sections
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // My Orders section
                      _buildProfileSection(
                        icon: Icons.shopping_bag,
                        title: 'My Orders',
                        onTap: () => Get.toNamed(Routes.orders),
                      ),
                      
                      // My Referrals section
                      _buildProfileSection(
                        icon: Icons.people,
                        title: 'My Referrals',
                        onTap: () => Get.toNamed(Routes.referral),
                      ),
                      
                      // Theme toggle
                      _buildProfileSection(
                        icon: themeProvider.isDarkMode
                            ? Icons.light_mode
                            : Icons.dark_mode,
                        title: themeProvider.isDarkMode
                            ? 'Light Mode'
                            : 'Dark Mode',
                        onTap: () => themeProvider.toggleTheme(),
                      ),
                      
                      // Language section
                      _buildProfileSection(
                        icon: Icons.language,
                        title: 'Language',
                        onTap: () {
                          // TODO: Show language selection dialog
                          Get.snackbar(
                            'Coming Soon',
                            'Language selection will be available soon!',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        },
                      ),
                      
                      // Help & Support section
                      _buildProfileSection(
                        icon: Icons.help,
                        title: 'Help & Support',
                        onTap: () {
                          // TODO: Navigate to help & support
                          Get.snackbar(
                            'Coming Soon',
                            'Help & Support will be available soon!',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        },
                      ),
                      
                      // About section
                      _buildProfileSection(
                        icon: Icons.info,
                        title: 'About',
                        onTap: () {
                          // TODO: Navigate to about
                          Get.snackbar(
                            'Coming Soon',
                            'About will be available soon!',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Logout button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => authProvider.logout(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.errorColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('Logout'),
                        ),
                      ),
                      
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
  
  Widget _buildNotLoggedInView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            const Icon(
              Icons.account_circle,
              size: 100,
              color: AppTheme.textLight,
            ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              'Please Login First',
              style: AppTheme.textTheme.headlineMedium,
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Description
            Text(
              'Login to access your profile and use all features of Laradrobe',
              style: AppTheme.textTheme.bodyLarge?.copyWith(
                color: AppTheme.textMedium,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Login button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Get.toNamed(Routes.login),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Login'),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildProfileSection({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppTheme.accentColor,
        ),
        title: Text(title),
        trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }
}
