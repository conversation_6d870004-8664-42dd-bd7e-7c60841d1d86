import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/category_selector.dart';
import 'package:Laradrobe/widgets/custom_app_bar.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/filter_bottom_sheet.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:Laradrobe/widgets/product_card.dart';

class CategoryScreen extends StatefulWidget {
  const CategoryScreen({Key? key}) : super(key: key);

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  final homeProvider = Get.find<HomeProvider>();
  final locationProvider = Get.find<LocationProvider>();
  final categoryProvider = Get.find<CategoryProvider>();
  
  final ScrollController _scrollController = ScrollController();
  final RxString _selectedCategory = ''.obs;
  final RxString _selectedCity = ''.obs;
  // Price range removed for users - only category and city filters available
  
  @override
  void initState() {
    super.initState();
    _initializeFilters();
    _setupScrollController();
  }
  
  void _initializeFilters() {
    // Get category from parameters
    final category = Get.parameters['category'];
    if (category != null && category.isNotEmpty) {
      _selectedCategory.value = category;
      categoryProvider.setSelectedCategory(category);
    }
    
    // Initialize city from location provider
    if (locationProvider.city.value.isNotEmpty) {
      _selectedCity.value = locationProvider.city.value;
    }
    
    // Fetch products
    _fetchProducts();
  }
  
  void _setupScrollController() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        // Load more products when reaching the end of the list
        if (!homeProvider.isLoading.value && !homeProvider.isLastPage.value) {
          homeProvider.loadMoreProducts(
            _selectedCity.value,
            category: _selectedCategory.value.isNotEmpty
                ? _selectedCategory.value
                : null,
          );
        }
      }
    });
  }
  
  Future<void> _fetchProducts() async {
    homeProvider.resetAndFetchProducts(
      _selectedCity.value,
      category: _selectedCategory.value.isNotEmpty
          ? _selectedCategory.value
          : null,
    );
  }
  
  void _showFilterBottomSheet() {
    FilterBottomSheet.show(
      context: context,
      selectedCategory: _selectedCategory.value,
      selectedCity: _selectedCity.value,
      priceRange: null, // Price range removed for users
      onApplyFilters: (filters) {
        _selectedCategory.value = filters['category'] as String;
        _selectedCity.value = filters['city'] as String;
        // Price range filtering removed for users

        // Update category provider
        categoryProvider.setSelectedCategory(_selectedCategory.value);

        // Fetch products with new filters
        _fetchProducts();
      },
      onResetFilters: () {
        _selectedCategory.value = '';
        _selectedCity.value = locationProvider.city.value;
        // Price range reset removed for users

        // Update category provider
        categoryProvider.clearSelectedCategory();

        // Fetch products with reset filters
        _fetchProducts();
      },
    );
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Categories',
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => Get.toNamed(Routes.search),
          ),
        ],
      ),
      body: Column(
        children: [
          // Category selector
          Obx(() => CategoryGridSelector(
            selectedCategory: _selectedCategory.value,
            onCategorySelected: (category) {
              _selectedCategory.value = category;
              categoryProvider.setSelectedCategory(category);
              _fetchProducts();
            },
            crossAxisCount: 3,
          )),
          
          // Products
          Expanded(
            child: Obx(() {
              if (homeProvider.isInitialLoading.value) {
                return const Center(
                  child: LoadingIndicator(),
                );
              }
              
              if (homeProvider.products.isEmpty) {
                return EmptyState(
                  icon: Icons.search_off,
                  title: 'No Products Found',
                  message: 'We couldn\'t find any products matching your criteria. Try different filters.',
                  buttonText: 'Change Filters',
                  onButtonPressed: _showFilterBottomSheet,
                );
              }
              
              return RefreshIndicator(
                onRefresh: _fetchProducts,
                child: GridView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.7,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: homeProvider.products.length + (homeProvider.isLoadingMore.value ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == homeProvider.products.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }
                    
                    final product = homeProvider.products[index];
                    return ProductCard(
                      product: product,
                      onTap: (productId) => Get.toNamed(
                        Routes.productDetail,
                        parameters: {'productId': productId},
                      ),
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showFilterBottomSheet,
        backgroundColor: AppTheme.accentColor,
        foregroundColor: Colors.white,
        child: const Icon(Icons.filter_list),
      ),
    );
  }
}
