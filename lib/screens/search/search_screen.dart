import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/custom_app_bar.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/filter_bottom_sheet.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:Laradrobe/widgets/product_card.dart';
import 'package:Laradrobe/widgets/shimmer_loading.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({Key? key}) : super(key: key);

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final RxList<Map<String, dynamic>> _searchResults = <Map<String, dynamic>>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _isInitialSearch = true.obs;
  final RxString _selectedCategory = ''.obs;
  final RxString _selectedCity = ''.obs;
  // Price range removed for users - only category and city filters available
  
  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }
  
  void _initializeFilters() {
    // Initialize city from location provider
    final locationProvider = Get.find<LocationProvider>();
    if (locationProvider.city.value.isNotEmpty) {
      _selectedCity.value = locationProvider.city.value;
    }
  }
  
  Future<void> _search() async {
    final query = _searchController.text.trim();
    if (query.isEmpty) return;
    
    _isLoading.value = true;
    _isInitialSearch.value = false;
    
    try {
      // TODO: Replace with actual search API call
      // This is a placeholder implementation
      await Future.delayed(const Duration(seconds: 1));
      
      // Simulate search results
      final results = await ApiService.fetchProductsByCityCategory(
        _selectedCity.value,
        _selectedCategory.value.isNotEmpty ? _selectedCategory.value : null,
        1,
      );
      
      // Price filtering removed for users - show all results
      _searchResults.value = results;
    } catch (e) {
      print('Error searching products: $e');
      Get.snackbar(
        'Error',
        'Failed to search products. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  void _showFilterBottomSheet() {
    FilterBottomSheet.show(
      context: context,
      selectedCategory: _selectedCategory.value,
      selectedCity: _selectedCity.value,
      priceRange: null, // Price range removed for users
      onApplyFilters: (filters) {
        _selectedCategory.value = filters['category'] as String;
        _selectedCity.value = filters['city'] as String;
        // Price range filtering removed for users

        // Re-run search with new filters
        _search();
      },
      onResetFilters: () {
        _selectedCategory.value = '';
        _selectedCity.value = '';
        // Price range reset removed for users
      },
    );
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SearchAppBar(
        hintText: 'Search products...',
        controller: _searchController,
        onSubmitted: (_) => _search(),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const ShimmerProductGrid();
        }
        
        if (_isInitialSearch.value) {
          return const Center(
            child: Text('Search for products'),
          );
        }
        
        if (_searchResults.isEmpty) {
          return EmptyState(
            icon: Icons.search_off,
            title: 'No Results Found',
            message: 'We couldn\'t find any products matching your search criteria. Try different keywords or filters.',
            buttonText: 'Modify Filters',
            onButtonPressed: _showFilterBottomSheet,
          );
        }
        
        return GridView.builder(
          padding: const EdgeInsets.all(8),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.7,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _searchResults.length,
          itemBuilder: (context, index) {
            final product = _searchResults[index];
            return ProductCard(
              product: product,
              onTap: (productId) => Get.toNamed(
                Routes.productDetail,
                parameters: {'productId': productId},
              ),
            );
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: _search,
        backgroundColor: AppTheme.accentColor,
        foregroundColor: Colors.white,
        child: const Icon(Icons.search),
      ),
    );
  }
}
