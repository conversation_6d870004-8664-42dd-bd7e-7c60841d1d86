import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:Laradrobe/config/env.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:Laradrobe/api/api_service.dart';

class FeaturedProductsSlider extends StatefulWidget {
  final List<Map<String, dynamic>> products;
  final Function(String) onProductTap;

  const FeaturedProductsSlider({
    Key? key,
    required this.products,
    required this.onProductTap,
  }) : super(key: key);

  @override
  State<FeaturedProductsSlider> createState() => _FeaturedProductsSliderState();
}

class _FeaturedProductsSliderState extends State<FeaturedProductsSlider> {
  final PageController _pageController = PageController(viewportFraction: 0.85);
  int _currentIndex = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.products.isEmpty) {
      return const SizedBox.shrink();
    }

    // Take only first 5 products for the slider
    final featuredProducts = widget.products.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Featured Products',
                style: AppTheme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDark,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all products or category page
                  Get.toNamed('/category');
                },
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: AppTheme.accentColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Product Slider
        SizedBox(
          height: 280,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: featuredProducts.length,
            itemBuilder: (context, index) {
              final product = featuredProducts[index];
              return _buildProductCard(product, index);
            },
          ),
        ),

        const SizedBox(height: 16),

        // Page Indicators
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            featuredProducts.length,
            (index) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentIndex == index ? 24 : 8,
              height: 8,
              decoration: BoxDecoration(
                color: _currentIndex == index
                    ? AppTheme.accentColor
                    : AppTheme.accentColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product, int index) {
    final authProvider = Get.find<AuthProvider>();
    final homeProvider = Get.find<HomeProvider>();
    final isInCart = (product['is_in_cart'] ?? 0) > 0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: GestureDetector(
        onTap: () => _showImageGallery(context, product),
        child: Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              Expanded(
                flex: 3,
                child: Stack(
                  children: [
                    // Main Image
                    Container(
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(20),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(20),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: '${dotenv.env[Env.productImageUrl]}${product["thumbnail"]}',
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            color: AppTheme.backgroundLight,
                            child: const Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: AppTheme.backgroundLight,
                            child: const Icon(
                              Icons.image_not_supported,
                              color: AppTheme.textMedium,
                              size: 40,
                            ),
                          ),
                        ),
                      ),
                    ),

                    // Favorite Button
                    Positioned(
                      top: 12,
                      right: 12,
                      child: GestureDetector(
                        onTap: () async {
                          if (!authProvider.isAuthenticated.value) {
                            Get.snackbar(
                              'Login Required',
                              'Please login to add items to favorites',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: AppTheme.warningColor,
                              colorText: AppTheme.textDark,
                              margin: const EdgeInsets.all(16),
                              borderRadius: 12,
                              duration: const Duration(seconds: 3),
                            );
                            return;
                          }

                          // Show loading
                          Get.dialog(
                            const Center(
                              child: CircularProgressIndicator(),
                            ),
                            barrierDismissible: false,
                          );

                          // Toggle favorite
                          final success = await homeProvider.toggleFavorite(
                            product['id'],
                            category: product['category'],
                          );

                          // Close loading dialog
                          Get.back();

                          if (success) {
                            // Check the updated state after toggle
                            final updatedProduct = homeProvider.products.firstWhere(
                              (p) => p['id'] == product['id'],
                              orElse: () => homeProvider.newArrivals.firstWhere(
                                (p) => p['id'] == product['id'],
                                orElse: () => homeProvider.bestProducts.firstWhere(
                                  (p) => p['id'] == product['id'],
                                  orElse: () => product,
                                ),
                              ),
                            );
                            final isNowInCart = (updatedProduct['is_in_cart'] ?? 0) > 0;
                            
                            // Show success message
                            Get.snackbar(
                              isNowInCart ? 'Added to Favorites' : 'Removed from Favorites',
                              isNowInCart 
                                ? '${product['name']} added to your favorites'
                                : '${product['name']} removed from favorites',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: isNowInCart ? AppTheme.successColor : AppTheme.accentColor,
                              colorText: Colors.white,
                              margin: const EdgeInsets.all(16),
                              borderRadius: 12,
                              duration: const Duration(seconds: 2),
                              icon: Icon(
                                isNowInCart ? Icons.favorite : Icons.favorite_border,
                                color: Colors.white,
                              ),
                            );
                          } else {
                            // Show error message
                            Get.snackbar(
                              'Error',
                              'Failed to update favorites. Please try again.',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: const Color(0xFFD32F2F),
                              colorText: Colors.white,
                              margin: const EdgeInsets.all(16),
                              borderRadius: 12,
                              icon: const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                              ),
                            );
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            isInCart ? Icons.favorite : Icons.favorite_border,
                            color: isInCart ? Colors.red : AppTheme.textMedium,
                            size: 20,
                          ),
                        ),
                      ),
                    ),

                    // Featured Badge
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [AppTheme.accentColor, AppTheme.primaryColor],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Featured',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Product Details
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Top Row: Product Name and Rating
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left Side: Product Name
                          Expanded(
                            flex: 3,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  product['name'] ?? 'Unknown Product',
                                  style: AppTheme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textDark,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                // Brand Name
                                if (product['brand_name'] != null)
                                  Text(
                                    product['brand_name'],
                                    style: AppTheme.textTheme.bodySmall?.copyWith(
                                      color: AppTheme.textMedium,
                                    ),
                                  ),
                              ],
                            ),
                          ),

                          const SizedBox(width: 12),

                          // Right Side: Rating
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                // Rating
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.amber.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.star,
                                        color: Colors.amber,
                                        size: 14,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${product['rating'] ?? 4.5}',
                                        style: AppTheme.textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: Colors.amber[800],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Bottom Row: Purchase Cost and Category
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Left Side: Purchase Cost
                          if (product['purchase_cost'] != null && product['purchase_cost'] > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.accentColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.shopping_bag_outlined,
                                    size: 16,
                                    color: AppTheme.accentColor,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '₹${product['purchase_cost']}',
                                    style: AppTheme.textTheme.titleSmall?.copyWith(
                                      color: AppTheme.accentColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          else
                            // Placeholder for alignment when no price
                            const SizedBox(width: 100),

                          // Right Side: Category
                          if (product['category'] != null)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                product['category'],
                                style: AppTheme.textTheme.bodySmall?.copyWith(
                                  color: AppTheme.accentColor,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showImageGallery(BuildContext context, Map<String, dynamic> product) async {
    // Show loading dialog
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(
          color: AppTheme.accentColor,
        ),
      ),
      barrierDismissible: false,
    );

    try {
      // Fetch complete product details to get all images
      final response = await ApiService.getProductById(product['id'].toString());

      // Close loading dialog
      Get.back();

      if (response.isNotEmpty) {
        List<String> imageUrls = [];

        // Add thumbnail first if available
        if (response['thumbnail'] != null) {
          imageUrls.add('${dotenv.env[Env.productImageUrl]}${response['thumbnail']}');
        }

        // Add all product images from product_image array
        if (response['product_image'] != null && response['product_image'] is List) {
          for (var imageData in response['product_image']) {
            if (imageData is Map && imageData['image_name'] != null) {
              String imageUrl = '${dotenv.env[Env.productImageUrl]}${imageData['image_name']}';
              // Avoid duplicates
              if (!imageUrls.contains(imageUrl)) {
                imageUrls.add(imageUrl);
              }
            }
          }
        }

        // Fallback: if no product_image array, try images array
        if (imageUrls.length <= 1 && response['images'] != null && response['images'] is List) {
          for (var image in response['images']) {
            String? imageUrl;
            if (image is String) {
              imageUrl = '${dotenv.env[Env.productImageUrl]}$image';
            } else if (image is Map && image['image'] != null) {
              imageUrl = '${dotenv.env[Env.productImageUrl]}${image['image']}';
            }

            // Avoid duplicates
            if (imageUrl != null && !imageUrls.contains(imageUrl)) {
              imageUrls.add(imageUrl);
            }
          }
        }

        // If still no images, show error
        if (imageUrls.isEmpty) {
          Get.snackbar(
            'No Images',
            'No images available for this product',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppTheme.warningColor,
            colorText: AppTheme.textDark,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
          return;
        }

        // Show image gallery with all images
        Get.to(
          () => Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              backgroundColor: Colors.black,
              iconTheme: const IconThemeData(color: Colors.white),
              title: Text(
                response['name'] ?? product['name'] ?? 'Product Images',
                style: const TextStyle(color: Colors.white),
              ),
              actions: [
                // Image counter
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${imageUrls.length} ${imageUrls.length == 1 ? 'Image' : 'Images'}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: () {
                    Get.snackbar(
                      'Coming Soon',
                      'Share functionality will be available soon!',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: AppTheme.infoColor,
                      colorText: Colors.white,
                      margin: const EdgeInsets.all(16),
                      borderRadius: 12,
                      icon: const Icon(
                        Icons.share,
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ],
            ),
            body: Stack(
              children: [
                PhotoViewGallery.builder(
                  itemCount: imageUrls.length,
                  builder: (context, index) {
                    return PhotoViewGalleryPageOptions(
                      imageProvider: CachedNetworkImageProvider(imageUrls[index]),
                      minScale: PhotoViewComputedScale.contained,
                      maxScale: PhotoViewComputedScale.covered * 3.0,
                      heroAttributes: PhotoViewHeroAttributes(tag: 'featured_image_$index'),
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[900],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  color: Colors.white,
                                  size: 64,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Failed to load image',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                  scrollPhysics: const BouncingScrollPhysics(),
                  backgroundDecoration: const BoxDecoration(color: Colors.black),
                  pageController: PageController(),
                  onPageChanged: (index) {
                    // Optional: Add page indicator or other functionality
                  },
                ),

                // Page indicator (if multiple images)
                if (imageUrls.length > 1)
                  Positioned(
                    bottom: 30,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '1 of ${imageUrls.length}',
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          transition: Transition.fadeIn,
          duration: const Duration(milliseconds: 300),
        );
      } else {
        // Close loading dialog if still open
        if (Get.isDialogOpen ?? false) {
          Get.back();
        }

        Get.snackbar(
          'Error',
          'Failed to load product images. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFFD32F2F),
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to load product images. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F),
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    }
  }
}
