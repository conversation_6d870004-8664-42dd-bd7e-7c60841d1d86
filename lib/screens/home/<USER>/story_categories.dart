import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class StoryCategories extends StatelessWidget {
  final Function(String) onCategorySelected;

  const StoryCategories({
    Key? key,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Get.find<CategoryProvider>();

    print('StoryCategories build - CategoryProvider found: ${categoryProvider != null}');
    print('Current categories count: ${categoryProvider.categories.length}');
    print('Is loading: ${categoryProvider.isLoading.value}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Categories',
                style: AppTheme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // Navigate to all categories
                  Get.toNamed('/category');
                },
                child: Text(
                  'See All',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        SizedBox(
          height: 120,
          child: Obx(() {
            final categories = categoryProvider.categories;
            final isLoading = categoryProvider.isLoading.value;

            print('StoryCategories UI - categories.length: ${categories.length}, isLoading: $isLoading');
            print('Categories data: $categories');

            // Show loading indicator while fetching categories
            if (isLoading) {
              print('Showing loading indicator');
              return const Center(
                child: CircularProgressIndicator(
                  color: AppTheme.accentColor,
                ),
              );
            }

            // Show error message with retry if categories failed to load
            if (categories.isEmpty) {
              print('Showing categories not available message');
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.category_outlined,
                      color: AppTheme.textMedium,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Categories not available',
                      style: TextStyle(
                        color: AppTheme.textMedium,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          onPressed: () => categoryProvider.retryFetchCategories(),
                          child: Text(
                            'Retry',
                            style: TextStyle(
                              color: AppTheme.accentColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        TextButton(
                          onPressed: () => categoryProvider.forceFetchCategories(),
                          child: Text(
                            'Force Fetch',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () => categoryProvider.debugFetchCategories(),
                      child: Text(
                        'Debug Fetch',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: categories.length + 1, // +1 for "All" category
              itemBuilder: (context, index) {
                if (index == 0) {
                  // "All" category
                  return _buildCategoryStory(
                    name: 'All',
                    gradient: LinearGradient(
                      colors: AppTheme.gradients[0],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    isSelected: categoryProvider.selectedCategory.value.isEmpty,
                    onTap: () => onCategorySelected(''),
                  );
                }

                final category = categories[index - 1];
                final categoryName = category['name'] ?? 'Unknown';

                return _buildCategoryStory(
                  name: categoryName,
                  gradient: _getCategoryGradient(index - 1),
                  isSelected: categoryProvider.selectedCategory.value == categoryName,
                  onTap: () => onCategorySelected(categoryName),
                );
              },
            );
          }),
        ),
      ],
    );
  }

  Widget _buildCategoryStory({
    required String name,
    required Gradient gradient,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                gradient: gradient,
                borderRadius: BorderRadius.circular(20),
                border: isSelected
                    ? Border.all(
                        color: AppTheme.primaryColor,
                        width: 3,
                      )
                    : null,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Beautiful frosted glass effect overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.25),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                    ),
                  ),
                  // Text
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text(
                        name,
                        style: TextStyle(
                          color: AppTheme.textDark,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.3,
                          height: 1.2,
                          shadows: [
                            Shadow(
                              color: Colors.white.withOpacity(0.8),
                              blurRadius: 1,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? AppTheme.primaryColor : AppTheme.textMedium,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }



  Gradient _getCategoryGradient(int index) {
    // Use app's predefined gradients and cycle through them
    final appGradients = AppTheme.gradients;
    final selectedGradient = appGradients[index % appGradients.length];

    return LinearGradient(
      colors: selectedGradient,
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}
