import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/config/env.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:Laradrobe/api/api_service.dart';

class EnhancedProductGrid extends StatefulWidget {
  final List<Map<String, dynamic>> products;
  final Function(String) onProductTap;

  const EnhancedProductGrid({
    Key? key,
    required this.products,
    required this.onProductTap,
  }) : super(key: key);

  @override
  State<EnhancedProductGrid> createState() => _EnhancedProductGridState();
}

class _EnhancedProductGridState extends State<EnhancedProductGrid> {

  @override
  Widget build(BuildContext context) {
    if (widget.products.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recommended for You',
                style: AppTheme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.toNamed('/category');
                },
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.65,
              crossAxisSpacing: 12,
              mainAxisSpacing: 16,
            ),
            itemCount: widget.products.length > 6 ? 6 : widget.products.length,
            itemBuilder: (context, index) {
              final product = widget.products[index];
              return _buildEnhancedProductCard(product);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedProductCard(Map<String, dynamic> product) {
    final homeProvider = Get.find<HomeProvider>();

    return GestureDetector(
      onTap: () => _showImageGallery(context, product),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image with overlay
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: '${dotenv.env[Env.productImageUrl]}${product["thumbnail"]}' ?? '',
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[200],
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.image_not_supported),
                      ),
                    ),
                  ),
                  
                  // Favorite button
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () async {
                        // Check authentication first
                        final authProvider = Get.find<AuthProvider>();
                        if (!authProvider.isAuthenticated.value) {
                          Get.snackbar(
                            'Login Required',
                            'Please login to add items to favorites',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: AppTheme.warningColor,
                            colorText: AppTheme.textDark,
                            margin: const EdgeInsets.all(16),
                            borderRadius: 12,
                            duration: const Duration(seconds: 3),
                          );
                          return;
                        }

                        try {
                          // Show loading indicator
                          Get.dialog(
                            const Center(
                              child: CircularProgressIndicator(
                                color: AppTheme.accentColor,
                              ),
                            ),
                            barrierDismissible: false,
                          );

                          // Toggle favorite
                          print('Toggling favorite for product: ${product['id']}');
                          final success = await homeProvider.toggleFavorite(
                            product['id'],
                            category: product['category'],
                          );
                          print('Toggle favorite result: $success');

                          // Close loading dialog
                          Get.back();

                          if (success) {
                            // Check the updated state after toggle
                            final updatedProduct = homeProvider.products.firstWhere(
                              (p) => p['id'] == product['id'],
                              orElse: () => homeProvider.newArrivals.firstWhere(
                                (p) => p['id'] == product['id'],
                                orElse: () => homeProvider.bestProducts.firstWhere(
                                  (p) => p['id'] == product['id'],
                                  orElse: () => product,
                                ),
                              ),
                            );
                            final isNowInCart = (updatedProduct['is_in_cart'] ?? 0) > 0;

                            // Show success message
                            Get.snackbar(
                              isNowInCart ? 'Added to Favorites' : 'Removed from Favorites',
                              isNowInCart
                                ? '${product['name']} added to your favorites'
                                : '${product['name']} removed from favorites',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: isNowInCart ? AppTheme.successColor : AppTheme.accentColor, // Use accent color for better contrast
                              colorText: Colors.white,
                              margin: const EdgeInsets.all(16),
                              borderRadius: 12,
                              duration: const Duration(seconds: 2),
                              icon: Icon(
                                isNowInCart ? Icons.favorite : Icons.favorite_border,
                                color: Colors.white,
                              ),
                            );
                          } else {
                            // Show error message
                            Get.snackbar(
                              'Error',
                              'Failed to update favorites. Please try again.',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
                              colorText: Colors.white,
                              margin: const EdgeInsets.all(16),
                              borderRadius: 12,
                              icon: const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                              ),
                            );
                          }
                        } catch (e) {
                          // Close loading dialog if still open
                          if (Get.isDialogOpen ?? false) {
                            Get.back();
                          }

                          // Show error message
                          Get.snackbar(
                            'Error',
                            'Something went wrong. Please try again.',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
                            colorText: Colors.white,
                            margin: const EdgeInsets.all(16),
                            borderRadius: 12,
                            icon: const Icon(
                              Icons.error_outline,
                              color: Colors.white,
                            ),
                          );
                        }
                      },
                      child: Obx(() {
                        // Find the current product state from HomeProvider to get updated is_in_cart value
                        final currentProduct = homeProvider.products.firstWhere(
                          (p) => p['id'] == product['id'],
                          orElse: () => homeProvider.newArrivals.firstWhere(
                            (p) => p['id'] == product['id'],
                            orElse: () => homeProvider.bestProducts.firstWhere(
                              (p) => p['id'] == product['id'],
                              orElse: () => product,
                            ),
                          ),
                        );
                        final isInCart = (currentProduct['is_in_cart'] ?? 0) > 0;
                        return Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            isInCart ? Icons.favorite : Icons.favorite_border,
                            color: isInCart ? Colors.red : Colors.grey[600],
                            size: 18,
                          ),
                        );
                      }),
                    ),
                  ),
                  
                  // Discount badge (if applicable)
                  if (product['discount'] != null && product['discount'] > 0)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${product['discount']}% OFF',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            // Product details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top Row: Product Name and Rating
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left Side: Product Name
                        Expanded(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product['name'] ?? 'Unknown Product',
                                style: AppTheme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              // Brand Name
                              if (product['brand_name'] != null)
                                Text(
                                  product['brand_name'],
                                  style: AppTheme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.textMedium,
                                    fontSize: 11,
                                  ),
                                ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Right Side: Rating and Category
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              // Rating
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 3,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.amber.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.star,
                                      color: Colors.amber,
                                      size: 12,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      '${product['rating'] ?? 4.5}',
                                      style: AppTheme.textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: Colors.amber[800],
                                        fontSize: 11,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 4),

                              // Category Tag
                              if (product['category'] != null)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    product['category'],
                                    style: AppTheme.textTheme.bodySmall?.copyWith(
                                      color: AppTheme.accentColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 9,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const Spacer(),

                    // Bottom Row: Purchase Cost and Availability
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Left Side: Purchase Cost
                        if (product['purchase_cost'] != null && product['purchase_cost'] > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.accentColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.shopping_bag_outlined,
                                  size: 12,
                                  color: AppTheme.accentColor,
                                ),
                                const SizedBox(width: 3),
                                Text(
                                  '₹${product['purchase_cost']}',
                                  style: AppTheme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.accentColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 11,
                                  ),
                                ),
                              ],
                            ),
                          )
                        else
                          // Placeholder for alignment when no price
                          const SizedBox(width: 60),

                        // Right Side: Available Status
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: AppTheme.successColor,
                                size: 10,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                'Available',
                                style: AppTheme.textTheme.bodySmall?.copyWith(
                                  color: AppTheme.successColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showImageGallery(BuildContext context, Map<String, dynamic> product) async {
    // Show loading dialog
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(
          color: AppTheme.accentColor,
        ),
      ),
      barrierDismissible: false,
    );

    try {
      // Fetch complete product details to get all images
      final response = await ApiService.getProductById(product['id'].toString());

      // Close loading dialog
      Get.back();

      if (response.isNotEmpty) {
        List<String> imageUrls = [];

        // Add thumbnail first if available
        if (response['thumbnail'] != null) {
          imageUrls.add('${dotenv.env[Env.productImageUrl]}${response['thumbnail']}');
        }

        // Add all product images from product_image array
        if (response['product_image'] != null && response['product_image'] is List) {
          for (var imageData in response['product_image']) {
            if (imageData is Map && imageData['image_name'] != null) {
              String imageUrl = '${dotenv.env[Env.productImageUrl]}${imageData['image_name']}';
              // Avoid duplicates
              if (!imageUrls.contains(imageUrl)) {
                imageUrls.add(imageUrl);
              }
            }
          }
        }

        // Fallback: if no product_image array, try images array
        if (imageUrls.length <= 1 && response['images'] != null && response['images'] is List) {
          for (var image in response['images']) {
            String? imageUrl;
            if (image is String) {
              imageUrl = '${dotenv.env[Env.productImageUrl]}$image';
            } else if (image is Map && image['image'] != null) {
              imageUrl = '${dotenv.env[Env.productImageUrl]}${image['image']}';
            }

            // Avoid duplicates
            if (imageUrl != null && !imageUrls.contains(imageUrl)) {
              imageUrls.add(imageUrl);
            }
          }
        }

        // If still no images, show error
        if (imageUrls.isEmpty) {
          Get.snackbar(
            'No Images',
            'No images available for this product',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppTheme.warningColor,
            colorText: AppTheme.textDark,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
          return;
        }

        // Show image gallery with all images
        Get.to(
          () => Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              backgroundColor: Colors.black,
              iconTheme: const IconThemeData(color: Colors.white),
              title: Text(
                response['name'] ?? product['name'] ?? 'Product Images',
                style: const TextStyle(color: Colors.white),
              ),
              actions: [
                // Image counter
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${imageUrls.length} ${imageUrls.length == 1 ? 'Image' : 'Images'}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: () {
                    Get.snackbar(
                      'Coming Soon',
                      'Share functionality will be available soon!',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: AppTheme.infoColor,
                      colorText: Colors.white,
                      margin: const EdgeInsets.all(16),
                      borderRadius: 12,
                      icon: const Icon(
                        Icons.share,
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ],
            ),
            body: Stack(
              children: [
                PhotoViewGallery.builder(
                  itemCount: imageUrls.length,
                  builder: (context, index) {
                    return PhotoViewGalleryPageOptions(
                      imageProvider: CachedNetworkImageProvider(imageUrls[index]),
                      minScale: PhotoViewComputedScale.contained,
                      maxScale: PhotoViewComputedScale.covered * 3.0,
                      heroAttributes: PhotoViewHeroAttributes(tag: 'product_image_$index'),
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[900],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  color: Colors.white,
                                  size: 64,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Failed to load image',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                  scrollPhysics: const BouncingScrollPhysics(),
                  backgroundDecoration: const BoxDecoration(color: Colors.black),
                  pageController: PageController(),
                  onPageChanged: (index) {
                    // Optional: Add page indicator or other functionality
                  },
                ),

                // Page indicator (if multiple images)
                if (imageUrls.length > 1)
                  Positioned(
                    bottom: 30,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '1 of ${imageUrls.length}',
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          transition: Transition.fadeIn,
          duration: const Duration(milliseconds: 300),
        );
      } else {
        // Close loading dialog if still open
        if (Get.isDialogOpen ?? false) {
          Get.back();
        }

        Get.snackbar(
          'Error',
          'Failed to load product images. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFFD32F2F),
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to load product images. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F),
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    }
  }
}
