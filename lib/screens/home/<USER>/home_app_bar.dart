import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class HomeAppBar extends StatelessWidget {
  final String city;
  final VoidCallback onCityTap;

  const HomeAppBar({
    Key? key,
    required this.city,
    required this.onCityTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    
    return SliverAppBar(
      floating: true,
      snap: true,
      elevation: 0,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      title: Row(
        children: [
          // Logo
          Image.asset(
            'assets/images/logo.png',
            height: 80,
          ),
          
          const Spacer(),
          
          // City selector
          GestureDetector(
            onTap: onCityTap,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: AppTheme.backgroundLight,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: AppTheme.accentColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    city,
                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Icon(
                    Icons.arrow_drop_down,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
        ],
      ),
      automaticallyImplyLeading: false,
    );
  }
}
