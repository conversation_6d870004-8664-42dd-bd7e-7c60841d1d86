import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class CategorySlider extends StatelessWidget {
  final Function(String) onCategorySelected;

  const CategorySlider({
    Key? key,
    required this.onCategorySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Get.find<CategoryProvider>();
    
    return Obx(() {
      if (categoryProvider.isLoading.value) {
        return const SizedBox(
          height: 120,
          child: Center(
            child: LoadingIndicator(size: 24),
          ),
        );
      }
      
      return Container(
        height: 120,
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Text(
                    'Categories',
                    style: AppTheme.textTheme.titleMedium,
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      // Clear selected category and refresh products
                      categoryProvider.clearSelectedCategory();
                      onCategorySelected('');
                    },
                    child: Text(
                      'View All',
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: categoryProvider.categories.length,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                itemBuilder: (context, index) {
                  final category = categoryProvider.categories[index];
                  final isSelected = categoryProvider.selectedCategory.value == category['name'];
                  
                  return GestureDetector(
                    onTap: () => onCategorySelected(category['name']),
                    child: Container(
                      width: 70,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Category image
                          Container(
                            width: 60,
                            height: 35,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected ? AppTheme.primaryColor : AppTheme.backgroundLight,
                              border: isSelected
                                  ? Border.all(color: AppTheme.accentColor, width: 2)
                                  : null,
                            ),
                            child: Center(
                              child: Image.asset(
                                'assets/images/${category['image']}',
                                width: 40,
                                height: 40,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 4),
                          
                          // Category name
                          Text(
                            category['name'],
                            style: AppTheme.textTheme.bodySmall?.copyWith(
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              color: isSelected ? AppTheme.accentColor : AppTheme.textMedium,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}
