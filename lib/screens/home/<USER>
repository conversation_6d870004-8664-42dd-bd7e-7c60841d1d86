import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/screens/home/<USER>/hero_section.dart';
import 'package:Laradrobe/screens/home/<USER>/featured_products_slider.dart';
import 'package:Laradrobe/screens/home/<USER>/story_categories.dart';
import 'package:Laradrobe/screens/home/<USER>/quick_actions.dart';
import 'package:Laradrobe/screens/home/<USER>/featured_collections.dart';
import 'package:Laradrobe/screens/home/<USER>/enhanced_product_grid.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  final homeProvider = Get.find<HomeProvider>();
  final locationProvider = Get.find<LocationProvider>();
  final categoryProvider = Get.find<CategoryProvider>();

  @override
  void initState() {
    super.initState();

    // Setup scroll controller for pagination
    _scrollController.addListener(_onScroll);

    // Initialize data after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initData();
    });
  }

  void _initData() {
    // Fetch categories
    categoryProvider.fetchCategories();

    // Wait for cities to load, then set default city if needed and fetch products
    _loadCitiesAndFetchProducts();
  }

  Future<void> _loadCitiesAndFetchProducts() async {
    // If city is already set, fetch products directly
    if (locationProvider.city.value.isNotEmpty) {
      homeProvider.fetchProducts(
        locationProvider.city.value,
        category: categoryProvider.selectedCategory.value.isNotEmpty
            ? categoryProvider.selectedCategory.value
            : null,
      );
      return;
    }

    // If cities are still loading, wait for them
    if (locationProvider.isLoadingCities.value) {
      // Add a listener to detect when cities are loaded
      _citiesLoadingWorker = ever(locationProvider.isLoadingCities, (isLoading) {
        if (!isLoading && locationProvider.cities.isNotEmpty) {
          _setDefaultCityAndFetchProducts();
          // Dispose the worker after it's used
          _citiesLoadingWorker?.dispose();
          _citiesLoadingWorker = null;
        }
      });
    } else if (locationProvider.cities.isNotEmpty) {
      // Cities are already loaded, set default city
      _setDefaultCityAndFetchProducts();
    } else {
      // Force fetch cities if they're not loaded
      await locationProvider.fetchCities();
      if (locationProvider.cities.isNotEmpty) {
        _setDefaultCityAndFetchProducts();
      }
    }
  }

  void _setDefaultCityAndFetchProducts() {
    // Only set default city if no city is selected
    if (locationProvider.city.value.isEmpty && locationProvider.cities.isNotEmpty) {
      // Set the first city as default
      final defaultCity = locationProvider.cities[0]['name'] ?? locationProvider.cities[0]['city'] ?? 'Unknown City';
      print('Setting default city from backend: $defaultCity'); // Debug log
      locationProvider.setCity(defaultCity);

      // Fetch products with the default city
      homeProvider.fetchProducts(
        defaultCity,
        category: categoryProvider.selectedCategory.value.isNotEmpty
            ? categoryProvider.selectedCategory.value
            : null,
      );
    } else if (locationProvider.city.value.isNotEmpty) {
      // City is already set, just fetch products
      print('City already set: ${locationProvider.city.value}'); // Debug log
      homeProvider.fetchProducts(
        locationProvider.city.value,
        category: categoryProvider.selectedCategory.value.isNotEmpty
            ? categoryProvider.selectedCategory.value
            : null,
      );
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      // Load more products when reaching the end of the list
      if (!homeProvider.isLoading.value && !homeProvider.isLastPage.value) {
        homeProvider.loadMoreProducts(
          locationProvider.city.value,
          category: categoryProvider.selectedCategory.value.isNotEmpty
              ? categoryProvider.selectedCategory.value
              : null,
        );
      }
    }
  }

  void _openCitySelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildCitySelector(),
    );
  }

  Widget _buildCitySelector() {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      minChildSize: 0.4,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) {
        return Obx(() {
          final cities = locationProvider.cities;

          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Text(
                      'Select City',
                      style: AppTheme.textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: locationProvider.isLoadingCities.value
                    ? const LoadingIndicator()
                    : ListView.builder(
                        controller: scrollController,
                        itemCount: cities.length,
                        itemBuilder: (context, index) {
                          final city = cities[index];
                          final cityName = city['name'] ?? city['city'] ?? 'Unknown City';
                          return ListTile(
                            title: Text(cityName),
                            trailing: locationProvider.city.value == cityName
                                ? const Icon(Icons.check, color: AppTheme.accentColor)
                                : null,
                            onTap: () {
                              locationProvider.setCity(cityName);
                              homeProvider.resetAndFetchProducts(
                                cityName,
                                category: categoryProvider.selectedCategory.value.isNotEmpty
                                    ? categoryProvider.selectedCategory.value
                                    : null,
                              );
                              Navigator.pop(context);
                            },
                          );
                        },
                      ),
              ),
            ],
          );
        });
      },
    );
  }

  Widget _buildSliderPlaceholder() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header Placeholder
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 150,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.backgroundLight,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              Container(
                width: 60,
                height: 20,
                decoration: BoxDecoration(
                  color: AppTheme.backgroundLight,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Slider Placeholder
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: 3,
            itemBuilder: (context, index) {
              return Container(
                width: 220,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundLight,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    // Image placeholder
                    Expanded(
                      flex: 3,
                      child: Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppTheme.backgroundLight,
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(20),
                          ),
                        ),
                      ),
                    ),
                    // Content placeholder
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: double.infinity,
                              height: 16,
                              decoration: BoxDecoration(
                                color: AppTheme.backgroundLight,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: 100,
                              height: 12,
                              decoration: BoxDecoration(
                                color: AppTheme.backgroundLight,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            const Spacer(),
                            Container(
                              width: 80,
                              height: 20,
                              decoration: BoxDecoration(
                                color: AppTheme.backgroundLight,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),

        // Indicators placeholder
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            3,
            (index) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: AppTheme.backgroundLight,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Worker for cities loading listener
  Worker? _citiesLoadingWorker;

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();

    // Dispose of any active workers
    _citiesLoadingWorker?.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Obx(() {
          // Check if location is selected
          if (locationProvider.city.value.isEmpty) {
            return EmptyState(
              icon: Icons.location_city,
              title: 'Select a City',
              message: 'Please select a city to see products available in your area',
              buttonText: 'Select City',
              onButtonPressed: _openCitySelector,
            );
          }

          return CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Hero Section
                SliverToBoxAdapter(
                  child: HeroSection(
                    city: locationProvider.city.value,
                    onNotificationTap: () {
                      Get.snackbar(
                        'Notifications',
                        'No new notifications',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                    onCityTap: _openCitySelector,
                  ),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 24)),

                // Featured Products Slider
                SliverToBoxAdapter(
                  child: homeProvider.isInitialLoading.value
                      ? _buildSliderPlaceholder()
                      : homeProvider.products.isNotEmpty
                          ? FeaturedProductsSlider(
                              products: homeProvider.products,
                              onProductTap: (productId) {
                                // No longer needed - handled by image gallery
                              },
                            )
                          : const SizedBox.shrink(),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 24)),

                // Story Categories
                SliverToBoxAdapter(
                  child: StoryCategories(
                    onCategorySelected: (category) {
                      categoryProvider.setSelectedCategory(category);
                      homeProvider.resetAndFetchProducts(
                        locationProvider.city.value,
                        category: category.isNotEmpty ? category : null,
                      );
                    },
                  ),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 24)),

                // Quick Actions
                const SliverToBoxAdapter(
                  child: QuickActions(),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 24)),

                // Featured Collections
                SliverToBoxAdapter(
                  child: FeaturedCollections(
                    newArrivals: homeProvider.newArrivals,
                    bestProducts: homeProvider.bestProducts,
                  ),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 24)),

                // Enhanced Product Grid
                SliverToBoxAdapter(
                  child: homeProvider.isInitialLoading.value
                      ? const Padding(
                          padding: EdgeInsets.all(40),
                          child: Center(
                            child: LoadingIndicator(),
                          ),
                        )
                      : homeProvider.products.isEmpty
                          ? EmptyState(
                              icon: Icons.search_off,
                              title: 'No Products Found',
                              message: 'We couldn\'t find any products in ${locationProvider.city.value}${categoryProvider.selectedCategory.value.isNotEmpty ? ' for ${categoryProvider.selectedCategory.value}' : ''}',
                              buttonText: 'Change City',
                              onButtonPressed: _openCitySelector,
                            )
                          : EnhancedProductGrid(
                              products: homeProvider.products,
                              onProductTap: (productId) {
                                // No longer needed - handled by image gallery
                              },
                            ),
                ),

                // Loading indicator for pagination
                if (homeProvider.isLoadingMore.value)
                  const SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Center(
                        child: LoadingIndicator(size: 24),
                      ),
                    ),
                  ),

                // Bottom padding
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            );
        }),
      ),
    );
  }
}
