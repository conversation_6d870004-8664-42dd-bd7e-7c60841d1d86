import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/config/env.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/cart_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:Laradrobe/api/api_service.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({Key? key}) : super(key: key);

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Fetch cart when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshFavorites();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh favorites when app comes back to foreground
      _refreshFavorites();
    }
  }

  Future<void> _refreshFavorites() async {
    final authProvider = Get.find<AuthProvider>();
    final cartProvider = Get.find<CartProvider>();

    if (authProvider.isAuthenticated.value) {
      await cartProvider.fetchCart();
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    final cartProvider = Get.find<CartProvider>();

    return Scaffold(
      backgroundColor: AppTheme.background,
      body: CustomScrollView(
        slivers: [
          // Modern App Bar with gradient
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.gradients[0],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: FlexibleSpaceBar(
                title: Text(
                  'My Favorites',
                  style: AppTheme.textTheme.titleLarge?.copyWith(
                    color: AppTheme.textDark,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                centerTitle: true,
              ),
            ),
            actions: [
              Obx(() {
                if (cartProvider.cart.isNotEmpty) {
                  return Container(
                    margin: const EdgeInsets.only(right: 16),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${cartProvider.cart.length} items',
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.accentColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
            ],
          ),

          // Content with pull-to-refresh
          SliverFillRemaining(
            child: RefreshIndicator(
              onRefresh: () async {
                await _refreshFavorites();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Obx(() {
              // Check if user is authenticated
              if (!authProvider.isAuthenticated.value) {
                return _buildEmptyState(
                  icon: Icons.login_rounded,
                  title: 'Login Required',
                  message: 'Please login to view your favorites and save items you love',
                  buttonText: 'Login Now',
                  onButtonPressed: () => Get.toNamed(Routes.login),
                );
              }

              // Show loading indicator
              if (cartProvider.isLoading.value) {
                return const SizedBox(
                  height: 400,
                  child: Center(
                    child: LoadingIndicator(),
                  ),
                );
              }

              // Show empty state if cart is empty
              if (cartProvider.isEmpty.value) {
                return _buildEmptyState(
                  icon: Icons.favorite_border_rounded,
                  title: 'No Favorites Yet',
                  message: 'Start adding items to your favorites to see them here. Tap the heart icon on any product!',
                  buttonText: 'Explore Products',
                  onButtonPressed: () => Get.offAllNamed(Routes.home),
                );
              }

              // Show favorites content
              return _buildFavoritesContent(cartProvider);
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String message,
    required String buttonText,
    required VoidCallback onButtonPressed,
  }) {
    return Container(
      height: 500,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon with gradient background
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: AppTheme.gradients[0],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 60,
              color: AppTheme.accentColor,
            ),
          ),

          const SizedBox(height: 32),

          // Title
          Text(
            title,
            style: AppTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textDark,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Message
          Text(
            message,
            style: AppTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.textMedium,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Action button
          ElevatedButton(
            onPressed: onButtonPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 0,
            ),
            child: Text(
              buttonText,
              style: AppTheme.textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesContent(CartProvider cartProvider) {
    return Column(
      children: [
        // Stats section
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: AppTheme.gradients[1],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppTheme.accentColor.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                icon: Icons.favorite,
                value: '${cartProvider.cart.length}',
                label: 'Favorites',
              ),
              Container(
                width: 1,
                height: 40,
                color: AppTheme.accentColor.withOpacity(0.3),
              ),
              _buildStatItem(
                icon: Icons.category,
                value: '${_getUniqueCategories(cartProvider.cart).length}',
                label: 'Categories',
              ),
              Container(
                width: 1,
                height: 40,
                color: AppTheme.accentColor.withOpacity(0.3),
              ),
              _buildStatItem(
                icon: Icons.schedule,
                value: '${_getRecentlyAdded(cartProvider.cart)}',
                label: 'Recent',
              ),
            ],
          ),
        ),

        // Products grid
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.65,
              crossAxisSpacing: 12,
              mainAxisSpacing: 16,
            ),
            itemCount: cartProvider.cart.length,
            itemBuilder: (context, index) {
              final product = cartProvider.cart[index];
              return _buildEnhancedFavoriteCard(product, cartProvider);
            },
          ),
        ),

        const SizedBox(height: 100), // Bottom padding
      ],
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppTheme.accentColor,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.textDark,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.textMedium,
          ),
        ),
      ],
    );
  }

  Set<String> _getUniqueCategories(List<Map<String, dynamic>> products) {
    return products.map((product) => product['category'] as String).toSet();
  }

  String _getRecentlyAdded(List<Map<String, dynamic>> products) {
    // Show count of recently added items (last 7 days)
    // For now, just show total count as we don't have timestamp data
    return products.length.toString();
  }

  Widget _buildEnhancedFavoriteCard(Map<String, dynamic> product, CartProvider cartProvider) {
    final homeProvider = Get.find<HomeProvider>();

    return Dismissible(
      key: Key(product['id']),
      direction: DismissDirection.endToStart,
      background: Container(
        decoration: BoxDecoration(
          color: AppTheme.errorColor,
          borderRadius: BorderRadius.circular(16),
        ),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.delete_outline,
              color: Colors.white,
              size: 28,
            ),
            const SizedBox(height: 4),
            Text(
              'Remove',
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      onDismissed: (direction) {
        cartProvider.removeFromCart(product['id']);
        Get.snackbar(
          'Removed',
          '${product['name']} removed from favorites',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppTheme.accentColor, // Dark accent color for better contrast
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
          icon: const Icon(
            Icons.favorite_border,
            color: Colors.white,
          ),
        );
      },
      child: GestureDetector(
        onTap: () => _showImageGallery(context, product),
        child: Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image with overlay
              Expanded(
                flex: 3,
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(16),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: '${dotenv.env[Env.productImageUrl]}${product["thumbnail"]}' ?? '',
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: AppTheme.backgroundLight,
                          child: const Center(
                            child: CircularProgressIndicator(
                              color: AppTheme.accentColor,
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: AppTheme.backgroundLight,
                          child: Icon(
                            Icons.image_not_supported,
                            color: AppTheme.textMedium,
                            size: 40,
                          ),
                        ),
                      ),
                    ),

                    // Favorite button
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () async {
                          try {
                            // Show loading indicator
                            Get.dialog(
                              const Center(
                                child: CircularProgressIndicator(
                                  color: AppTheme.accentColor,
                                ),
                              ),
                              barrierDismissible: false,
                            );

                            // Remove from favorites
                            final success = await homeProvider.toggleFavorite(
                              product['id'],
                              category: product['category'],
                            );

                            // Close loading dialog
                            Get.back();

                            if (success) {
                              // Refresh the cart/favorites list
                              await cartProvider.fetchCart();

                              // Show success message
                              Get.snackbar(
                                'Removed',
                                '${product['name']} removed from favorites',
                                snackPosition: SnackPosition.BOTTOM,
                                backgroundColor: AppTheme.accentColor, // Dark accent color for better contrast
                                colorText: Colors.white,
                                margin: const EdgeInsets.all(16),
                                borderRadius: 12,
                                duration: const Duration(seconds: 2),
                                icon: const Icon(
                                  Icons.favorite_border,
                                  color: Colors.white,
                                ),
                              );
                            } else {
                              // Show error message
                              Get.snackbar(
                                'Error',
                                'Failed to remove from favorites. Please try again.',
                                snackPosition: SnackPosition.BOTTOM,
                                backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
                                colorText: Colors.white,
                                margin: const EdgeInsets.all(16),
                                borderRadius: 12,
                                icon: const Icon(
                                  Icons.error_outline,
                                  color: Colors.white,
                                ),
                              );
                            }
                          } catch (e) {
                            // Close loading dialog if still open
                            if (Get.isDialogOpen ?? false) {
                              Get.back();
                            }

                            // Show error message
                            Get.snackbar(
                              'Error',
                              'Something went wrong. Please try again.',
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
                              colorText: Colors.white,
                              margin: const EdgeInsets.all(16),
                              borderRadius: 12,
                              icon: const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                              ),
                            );
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: Colors.red,
                            size: 18,
                          ),
                        ),
                      ),
                    ),

                    // Category badge
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.accentColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          product['category'] ?? '',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Product details
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product name
                      Text(
                        product['name'] ?? 'Unknown Product',
                        style: AppTheme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Purchase Cost (if available)
                      if (product['purchase_cost'] != null && product['purchase_cost'] > 0)
                        Row(
                          children: [
                            Icon(
                              Icons.shopping_bag_outlined,
                              size: 14,
                              color: AppTheme.accentColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '₹${product['purchase_cost']}',
                              style: AppTheme.textTheme.titleSmall?.copyWith(
                                color: AppTheme.accentColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),

                      const Spacer(),

                      // Rating and availability
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Availability status
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.successColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: AppTheme.successColor,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Available',
                                  style: AppTheme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.successColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Rating
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryLightColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.star,
                                  color: Colors.amber,
                                  size: 14,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  '${product['rating'] ?? 4.5}',
                                  style: AppTheme.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showImageGallery(BuildContext context, Map<String, dynamic> product) async {
    // Show loading dialog
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(
          color: AppTheme.accentColor,
        ),
      ),
      barrierDismissible: false,
    );

    try {
      // Fetch complete product details to get all images
      final response = await ApiService.getProductById(product['id'].toString());

      // Close loading dialog
      Get.back();

      if (response.isNotEmpty) {
        List<String> imageUrls = [];

        // Add thumbnail first if available
        if (response['thumbnail'] != null) {
          imageUrls.add('${dotenv.env[Env.productImageUrl]}${response['thumbnail']}');
        }

        // Add all product images from product_image array
        if (response['product_image'] != null && response['product_image'] is List) {
          for (var imageData in response['product_image']) {
            if (imageData is Map && imageData['image_name'] != null) {
              String imageUrl = '${dotenv.env[Env.productImageUrl]}${imageData['image_name']}';
              // Avoid duplicates
              if (!imageUrls.contains(imageUrl)) {
                imageUrls.add(imageUrl);
              }
            }
          }
        }

        // Fallback: if no product_image array, try images array
        if (imageUrls.length <= 1 && response['images'] != null && response['images'] is List) {
          for (var image in response['images']) {
            String? imageUrl;
            if (image is String) {
              imageUrl = '${dotenv.env[Env.productImageUrl]}$image';
            } else if (image is Map && image['image'] != null) {
              imageUrl = '${dotenv.env[Env.productImageUrl]}${image['image']}';
            }

            // Avoid duplicates
            if (imageUrl != null && !imageUrls.contains(imageUrl)) {
              imageUrls.add(imageUrl);
            }
          }
        }

        // If still no images, show error
        if (imageUrls.isEmpty) {
          Get.snackbar(
            'No Images',
            'No images available for this product',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppTheme.warningColor,
            colorText: AppTheme.textDark,
            margin: const EdgeInsets.all(16),
            borderRadius: 12,
          );
          return;
        }

        // Show image gallery with all images
        Get.to(
          () => Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              backgroundColor: Colors.black,
              iconTheme: const IconThemeData(color: Colors.white),
              title: Text(
                response['name'] ?? product['name'] ?? 'Product Images',
                style: const TextStyle(color: Colors.white),
              ),
              actions: [
                // Image counter
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${imageUrls.length} ${imageUrls.length == 1 ? 'Image' : 'Images'}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: () {
                    Get.snackbar(
                      'Coming Soon',
                      'Share functionality will be available soon!',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: AppTheme.infoColor,
                      colorText: Colors.white,
                      margin: const EdgeInsets.all(16),
                      borderRadius: 12,
                      icon: const Icon(
                        Icons.share,
                        color: Colors.white,
                      ),
                    );
                  },
                ),
              ],
            ),
            body: Stack(
              children: [
                PhotoViewGallery.builder(
                  itemCount: imageUrls.length,
                  builder: (context, index) {
                    return PhotoViewGalleryPageOptions(
                      imageProvider: CachedNetworkImageProvider(imageUrls[index]),
                      minScale: PhotoViewComputedScale.contained,
                      maxScale: PhotoViewComputedScale.covered * 3.0,
                      heroAttributes: PhotoViewHeroAttributes(tag: 'favorites_image_$index'),
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[900],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  color: Colors.white,
                                  size: 64,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Failed to load image',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                  scrollPhysics: const BouncingScrollPhysics(),
                  backgroundDecoration: const BoxDecoration(color: Colors.black),
                  pageController: PageController(),
                  onPageChanged: (index) {
                    // Optional: Add page indicator or other functionality
                  },
                ),

                // Page indicator (if multiple images)
                if (imageUrls.length > 1)
                  Positioned(
                    bottom: 30,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '1 of ${imageUrls.length}',
                          style: const TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          transition: Transition.fadeIn,
          duration: const Duration(milliseconds: 300),
        );
      } else {
        // Close loading dialog if still open
        if (Get.isDialogOpen ?? false) {
          Get.back();
        }

        Get.snackbar(
          'Error',
          'Failed to load product images. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFFD32F2F),
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'Error',
        'Failed to load product images. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F),
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
      );
    }
  }
}
