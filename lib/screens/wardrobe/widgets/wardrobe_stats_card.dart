import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class WardrobeStatsCard extends StatefulWidget {
  final Map<String, List<dynamic>> wardrobe;

  const WardrobeStatsCard({
    Key? key,
    required this.wardrobe,
  }) : super(key: key);

  @override
  State<WardrobeStatsCard> createState() => _WardrobeStatsCardState();
}

class _WardrobeStatsCardState extends State<WardrobeStatsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStats();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildStatsCard(stats),
          ),
        );
      },
    );
  }

  Widget _buildStatsCard(Map<String, dynamic> stats) {
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppTheme.gradients[0], // Use app theme gradient
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.accentColor.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 25,
            offset: const Offset(0, 12),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Wardrobe Overview',
                style: AppTheme.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Stats Grid
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.checkroom,
                  label: 'Total Items',
                  value: stats['totalItems'].toString(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.category,
                  label: 'Categories',
                  value: stats['totalCategories'].toString(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.currency_rupee,
                  label: 'Total Value',
                  value: '₹${NumberFormat('#,##,###').format(stats['totalValue'])}',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.trending_up,
                  label: 'Avg. Price',
                  value: '₹${NumberFormat('#,###').format(stats['avgPrice'])}',
                ),
              ),
            ],
          ),
          
          if (stats['mostPopularCategory'] != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.star,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Most Popular Category',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                        Text(
                          '${stats['mostPopularCategory']} (${stats['mostPopularCount']} items)',
                          style: AppTheme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTheme.textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Map<String, dynamic> _calculateStats() {
    int totalItems = 0;
    int totalValue = 0;
    String? mostPopularCategory;
    int mostPopularCount = 0;

    for (final entry in widget.wardrobe.entries) {
      final category = entry.key;
      final products = entry.value;

      totalItems += products.length;

      // Calculate total value
      for (final product in products) {
        final price = product['rental_cost'] ?? product['price'] ?? 0;
        totalValue += (price as num).toInt();
      }

      // Find most popular category
      if (products.length > mostPopularCount) {
        mostPopularCount = products.length;
        mostPopularCategory = category;
      }
    }

    final avgPrice = totalItems > 0 ? (totalValue / totalItems).round() : 0;

    return {
      'totalItems': totalItems,
      'totalCategories': widget.wardrobe.length,
      'totalValue': totalValue,
      'avgPrice': avgPrice,
      'mostPopularCategory': mostPopularCategory,
      'mostPopularCount': mostPopularCount,
    };
  }
}
