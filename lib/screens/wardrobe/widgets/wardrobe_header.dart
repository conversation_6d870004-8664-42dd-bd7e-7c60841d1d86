import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class WardrobeHeader extends StatelessWidget {
  final String searchQuery;
  final Function(String) onSearchChanged;
  final VoidCallback onSearchToggle;
  final bool isSearching;
  final String viewMode;
  final Function(String) onViewModeChanged;
  final String sortBy;
  final Function(String) onSortChanged;

  const WardrobeHeader({
    Key? key,
    required this.searchQuery,
    required this.onSearchChanged,
    required this.onSearchToggle,
    required this.isSearching,
    required this.viewMode,
    required this.onViewModeChanged,
    required this.sortBy,
    required this.onSortChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        boxShadow: [
          BoxShadow(
            color: AppTheme.accentColor.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Top row with title and actions
          Row(
            children: [
              if (!isSearching) ...[
                Expanded(
                  child: Text(
                    'My Wardrobe',
                    style: AppTheme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textDark,
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  tooltip: 'Options',
                  onSelected: (value) {
                    if (value == 'search') {
                      onSearchToggle();
                    } else {
                      onSortChanged(value);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'search',
                      child: Row(
                        children: [
                          Icon(Icons.search, size: 20),
                          SizedBox(width: 8),
                          Text('Search'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'newest',
                      child: Row(
                        children: [
                          Icon(Icons.access_time, size: 20),
                          SizedBox(width: 8),
                          Text('Newest First'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'oldest',
                      child: Row(
                        children: [
                          Icon(Icons.history, size: 20),
                          SizedBox(width: 8),
                          Text('Oldest First'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'price_high',
                      child: Row(
                        children: [
                          Icon(Icons.trending_up, size: 20),
                          SizedBox(width: 8),
                          Text('Price: High to Low'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'price_low',
                      child: Row(
                        children: [
                          Icon(Icons.trending_down, size: 20),
                          SizedBox(width: 8),
                          Text('Price: Low to High'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'name',
                      child: Row(
                        children: [
                          Icon(Icons.sort_by_alpha, size: 20),
                          SizedBox(width: 8),
                          Text('Name A-Z'),
                        ],
                      ),
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () => Get.toNamed(Routes.productUpload),
                  tooltip: 'Add Product',
                ),
              ] else ...[
                // Search mode
                Expanded(
                  child: TextField(
                    autofocus: true,
                    onChanged: onSearchChanged,
                    decoration: InputDecoration(
                      hintText: 'Search your wardrobe...',
                      border: InputBorder.none,
                      hintStyle: AppTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textMedium,
                      ),
                    ),
                    style: AppTheme.textTheme.bodyLarge,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onSearchToggle,
                ),
              ],
            ],
          ),
          
          if (!isSearching) ...[
            const SizedBox(height: 16),
            // View mode toggles - more prominent
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppTheme.backgroundLight,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.textLight.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildViewModeButton(
                    icon: Icons.grid_view,
                    mode: 'grid',
                    label: 'Grid',
                  ),
                  _buildViewModeButton(
                    icon: Icons.view_list,
                    mode: 'list',
                    label: 'List',
                  ),
                  _buildViewModeButton(
                    icon: Icons.category,
                    mode: 'category',
                    label: 'Category',
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildViewModeButton({
    required IconData icon,
    required String mode,
    required String label,
  }) {
    final isSelected = viewMode == mode;

    return Expanded(
      child: GestureDetector(
        onTap: () => onViewModeChanged(mode),
        child: Container(
          margin: const EdgeInsets.all(2),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.accentColor : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: isSelected ? Colors.white : AppTheme.textMedium,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: AppTheme.textTheme.bodySmall?.copyWith(
                  color: isSelected ? Colors.white : AppTheme.textMedium,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
