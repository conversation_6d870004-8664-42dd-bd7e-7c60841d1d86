import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/wardrobe_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:Laradrobe/widgets/product_card.dart';

class WardrobeScreen extends StatelessWidget {
  const WardrobeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    final wardrobeProvider = Get.find<WardrobeProvider>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Wardrobe'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => Get.toNamed(Routes.productUpload),
          ),
        ],
      ),
      body: Obx(() {
        // Check if user is authenticated
        if (!authProvider.isAuthenticated.value) {
          return EmptyState(
            icon: Icons.login,
            title: 'Login Required',
            message: 'Please login to view your wardrobe',
            buttonText: 'Login',
            onButtonPressed: () => Get.toNamed(Routes.login),
          );
        }
        
        // Show loading indicator
        if (wardrobeProvider.isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }
        
        // Show empty state if wardrobe is empty
        if (wardrobeProvider.isEmpty.value) {
          return EmptyState(
            icon: Icons.checkroom,
            title: 'Your Wardrobe is Empty',
            message: 'Add products to your wardrobe to see them here',
            buttonText: 'Add Product',
            onButtonPressed: () => Get.toNamed(Routes.productUpload),
          );
        }
        
        // Show wardrobe content
        return RefreshIndicator(
          onRefresh: () => wardrobeProvider.fetchWardrobe(),
          child: CustomScrollView(
            slivers: [
              // Categories and products
              ...wardrobeProvider.wardrobe.entries.map((entry) {
                final category = entry.key;
                final products = entry.value;
                
                return SliverList(
                  delegate: SliverChildListDelegate([
                    // Category header
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                      child: Text(
                        category,
                        style: AppTheme.textTheme.titleLarge,
                      ),
                    ),
                    
                    // Products grid
                    GridView.builder(
                      padding: const EdgeInsets.all(8),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.7,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: products.length,
                      itemBuilder: (context, index) {
                        final product = products[index];
                        return ProductCard(
                          product: product,
                          onTap: (productId) => Get.toNamed(
                            Routes.productDetail,
                            parameters: {'productId': productId},
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 8),
                  ]),
                );
              }).toList(),
              
              // Bottom padding
              const SliverToBoxAdapter(
                child: SizedBox(height: 16),
              ),
            ],
          ),
        );
      }),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Get.toNamed(Routes.productUpload),
        backgroundColor: AppTheme.accentColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Add Item'),
      ),
    );
  }
}
