import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/wardrobe_provider.dart';
import 'package:Laradrobe/providers/product_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:Laradrobe/widgets/product_card.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:Laradrobe/config/env.dart';

class WardrobeScreen extends StatelessWidget {
  const WardrobeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    final wardrobeProvider = Get.find<WardrobeProvider>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Wardrobe'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => Get.toNamed(Routes.productUpload),
          ),
        ],
      ),
      body: Obx(() {
        // Check if user is authenticated
        if (!authProvider.isAuthenticated.value) {
          return EmptyState(
            icon: Icons.login,
            title: 'Login Required',
            message: 'Please login to view your wardrobe',
            buttonText: 'Login',
            onButtonPressed: () => Get.toNamed(Routes.login),
          );
        }
        
        // Show loading indicator
        if (wardrobeProvider.isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }
        
        // Show empty state if wardrobe is empty
        if (wardrobeProvider.isEmpty.value) {
          return EmptyState(
            icon: Icons.checkroom,
            title: 'Your Wardrobe is Empty',
            message: 'Add products to your wardrobe to see them here',
            buttonText: 'Add Product',
            onButtonPressed: () => Get.toNamed(Routes.productUpload),
          );
        }
        
        // Show wardrobe content
        return RefreshIndicator(
          onRefresh: () => wardrobeProvider.fetchWardrobe(),
          child: CustomScrollView(
            slivers: [
              // Categories and products
              ...wardrobeProvider.wardrobe.entries.map((entry) {
                final category = entry.key;
                final products = entry.value;
                
                return SliverList(
                  delegate: SliverChildListDelegate([
                    // Category header
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                      child: Text(
                        category,
                        style: AppTheme.textTheme.titleLarge,
                      ),
                    ),
                    
                    // Products grid
                    GridView.builder(
                      padding: const EdgeInsets.all(8),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.7,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: products.length,
                      itemBuilder: (context, index) {
                        final product = products[index];
                        return _buildWardrobeProductCard(
                          product: product,
                          category: category,
                          wardrobeProvider: wardrobeProvider,
                        );
                      },
                    ),
                    
                    const SizedBox(height: 8),
                  ]),
                );
              }).toList(),
              
              // Bottom padding
              const SliverToBoxAdapter(
                child: SizedBox(height: 16),
              ),
            ],
          ),
        );
      }),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "wardrobe_fab",
        onPressed: () => Get.toNamed(Routes.productUpload),
        backgroundColor: AppTheme.accentColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Add Item'),
      ),
    );
  }

  Widget _buildWardrobeProductCard({
    required Map<String, dynamic> product,
    required String category,
    required WardrobeProvider wardrobeProvider,
  }) {
    final productProvider = Get.find<ProductProvider>();
    final imageUrl = '${dotenv.env[Env.productImageUrl]}${product["thumbnail"]}';

    return GestureDetector(
      onTap: () => Get.toNamed(
        Routes.productDetail,
        parameters: {'productId': product['id']},
      ),
      onLongPress: () => _showProductActions(
        product: product,
        category: category,
        wardrobeProvider: wardrobeProvider,
        productProvider: productProvider,
      ),
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image with action buttons
            ClipRRect(
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
              child: Stack(
                children: [
                  // Product image
                  AspectRatio(
                    aspectRatio: 1,
                    child: Hero(
                      tag: 'wardrobe_product_${product['id']}',
                      child: CachedNetworkImage(
                        imageUrl: imageUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: AppTheme.backgroundLight,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: AppTheme.backgroundLight,
                          child: const Center(
                            child: Icon(
                              Icons.image_not_supported_outlined,
                              color: AppTheme.textLight,
                              size: 40,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Action buttons overlay
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Column(
                      children: [
                        // Edit button
                        GestureDetector(
                          onTap: () => Get.toNamed(
                            Routes.productUpdate,
                            parameters: {'productId': product['id']},
                          ),
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: AppTheme.accentColor.withOpacity(0.9),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.edit,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),

                        const SizedBox(height: 8),

                        // Delete button
                        GestureDetector(
                          onTap: () => _showDeleteConfirmation(
                            product: product,
                            category: category,
                            wardrobeProvider: wardrobeProvider,
                            productProvider: productProvider,
                          ),
                          child: Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.9),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.delete,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Product details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    product['name'],
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTheme.textTheme.titleMedium,
                  ),

                  const SizedBox(height: 4),

                  // Rating and availability
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${product['rating'] ?? 4.5}',
                        style: AppTheme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Available',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.successColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // Category
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryLightColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      product['category'],
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.accentColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation({
    required Map<String, dynamic> product,
    required String category,
    required WardrobeProvider wardrobeProvider,
    required ProductProvider productProvider,
  }) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Product'),
        content: Text(
          'Are you sure you want to delete "${product['name']}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back(); // Close dialog

              // Show loading dialog
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.accentColor,
                  ),
                ),
                barrierDismissible: false,
              );

              try {
                // Set the product ID in provider and delete
                productProvider.productId.value = product['id'];
                final success = await productProvider.deleteProduct();

                // Close loading dialog
                Get.back();

                if (success) {
                  // Remove from local wardrobe data
                  wardrobeProvider.removeProductFromWardrobe(category, product['id']);

                  // Show success message
                  Get.snackbar(
                    'Deleted',
                    '${product['name']} has been deleted successfully',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: AppTheme.successColor,
                    colorText: Colors.white,
                    margin: const EdgeInsets.all(16),
                    borderRadius: 12,
                    icon: const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                    ),
                  );
                } else {
                  // Show error message
                  Get.snackbar(
                    'Error',
                    'Failed to delete product. Please try again.',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: const Color(0xFFD32F2F),
                    colorText: Colors.white,
                    margin: const EdgeInsets.all(16),
                    borderRadius: 12,
                    icon: const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                    ),
                  );
                }
              } catch (e) {
                // Close loading dialog if still open
                if (Get.isDialogOpen ?? false) {
                  Get.back();
                }

                // Show error message
                Get.snackbar(
                  'Error',
                  'Something went wrong. Please try again.',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: const Color(0xFFD32F2F),
                  colorText: Colors.white,
                  margin: const EdgeInsets.all(16),
                  borderRadius: 12,
                  icon: const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showProductActions({
    required Map<String, dynamic> product,
    required String category,
    required WardrobeProvider wardrobeProvider,
    required ProductProvider productProvider,
  }) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 20),

            // Product info
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Hero(
                    tag: 'wardrobe_sheet_${product['id']}',
                    child: CachedNetworkImage(
                      imageUrl: '${dotenv.env[Env.productImageUrl]}${product["thumbnail"]}',
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[200],
                        child: const Icon(Icons.image_not_supported),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product['name'],
                        style: AppTheme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product['category'],
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.textMedium,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                // Edit button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Get.back(); // Close bottom sheet
                      Get.toNamed(
                        Routes.productUpdate,
                        parameters: {'productId': product['id']},
                      );
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('Edit'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Delete button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Get.back(); // Close bottom sheet
                      _showDeleteConfirmation(
                        product: product,
                        category: category,
                        wardrobeProvider: wardrobeProvider,
                        productProvider: productProvider,
                      );
                    },
                    icon: const Icon(Icons.delete),
                    label: const Text('Delete'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Cancel button
            TextButton(
              onPressed: () => Get.back(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              ),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }
}
