import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/wardrobe_provider.dart';
import 'package:Laradrobe/providers/product_provider.dart';
import 'package:Laradrobe/screens/wardrobe/widgets/wardrobe_header.dart';
import 'package:Laradrobe/screens/wardrobe/widgets/wardrobe_stats_card.dart';
import 'package:Laradrobe/screens/wardrobe/widgets/wardrobe_filter_bar.dart';
import 'package:Laradrobe/screens/wardrobe/widgets/enhanced_product_card.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class WardrobeScreen extends StatefulWidget {
  const WardrobeScreen({Key? key}) : super(key: key);

  @override
  State<WardrobeScreen> createState() => _WardrobeScreenState();
}

class _WardrobeScreenState extends State<WardrobeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // View mode: 'grid', 'list', 'category'
  String _viewMode = 'grid';
  String _sortBy = 'newest'; // 'newest', 'oldest', 'price_high', 'price_low', 'name'
  String _selectedCategory = 'All';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    final wardrobeProvider = Get.find<WardrobeProvider>();

    return Scaffold(
      body: Obx(() {
        // Check if user is authenticated
        if (!authProvider.isAuthenticated.value) {
          return EmptyState(
            icon: Icons.login,
            title: 'Login Required',
            message: 'Please login to view your wardrobe',
            buttonText: 'Login',
            onButtonPressed: () => Get.toNamed(Routes.login),
          );
        }

        // Show loading indicator
        if (wardrobeProvider.isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }

        // Show empty state if wardrobe is empty
        if (wardrobeProvider.isEmpty.value) {
          return Column(
            children: [
              WardrobeHeader(
                searchQuery: _searchController.text,
                onSearchChanged: (query) => setState(() {}),
                onSearchToggle: () => setState(() {
                  _isSearching = !_isSearching;
                  if (!_isSearching) {
                    _searchController.clear();
                  }
                }),
                isSearching: _isSearching,
                viewMode: _viewMode,
                onViewModeChanged: (mode) => setState(() => _viewMode = mode),
                sortBy: _sortBy,
                onSortChanged: (sort) => setState(() => _sortBy = sort),
              ),
              Expanded(
                child: EmptyState(
                  icon: Icons.checkroom,
                  title: 'Your Wardrobe is Empty',
                  message: 'Add products to your wardrobe to see them here',
                  buttonText: 'Add Product',
                  onButtonPressed: () => Get.toNamed(Routes.productUpload),
                ),
              ),
            ],
          );
        }

        // Show wardrobe content
        return _buildWardrobeContent(wardrobeProvider);
      }),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "wardrobe_fab",
        onPressed: () => Get.toNamed(Routes.productUpload),
        backgroundColor: AppTheme.accentColor,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('Add Item'),
      ),
    );
  }

  Widget _buildWardrobeContent(WardrobeProvider wardrobeProvider) {
    return RefreshIndicator(
      onRefresh: () => wardrobeProvider.fetchWardrobe(),
      child: CustomScrollView(
        slivers: [
          // Header
          SliverToBoxAdapter(
            child: WardrobeHeader(
              searchQuery: _searchController.text,
              onSearchChanged: (query) => setState(() {}),
              onSearchToggle: () => setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              }),
              isSearching: _isSearching,
              viewMode: _viewMode,
              onViewModeChanged: (mode) => setState(() => _viewMode = mode),
              sortBy: _sortBy,
              onSortChanged: (sort) => setState(() => _sortBy = sort),
            ),
          ),

          // Stats Card
          SliverToBoxAdapter(
            child: WardrobeStatsCard(
              wardrobe: wardrobeProvider.wardrobe,
            ),
          ),

          // Category Filter
          if (_viewMode != 'category')
            SliverToBoxAdapter(
              child: WardrobeFilterBar(
                categories: wardrobeProvider.wardrobe.keys.toList(),
                selectedCategory: _selectedCategory,
                onCategoryChanged: (category) => setState(() => _selectedCategory = category),
              ),
            ),

          // Products Content
          if (_viewMode == 'category')
            ..._buildCategoryView(wardrobeProvider)
          else
            ..._buildFilteredView(wardrobeProvider),

          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryView(WardrobeProvider wardrobeProvider) {
    final filteredWardrobe = _getFilteredWardrobe(wardrobeProvider.wardrobe);

    return filteredWardrobe.entries.map((entry) {
      final category = entry.key;
      final products = entry.value;

      return SliverList(
        delegate: SliverChildListDelegate([
          // Category header
          Container(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              children: [
                Icon(
                  _getCategoryIcon(category),
                  color: AppTheme.accentColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  category,
                  style: AppTheme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryLightColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${products.length} items',
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.accentColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Products grid/list
          _buildProductsLayout(products),

          const SizedBox(height: 16),
        ]),
      );
    }).toList();
  }

  List<Widget> _buildFilteredView(WardrobeProvider wardrobeProvider) {
    final allProducts = _getAllFilteredProducts(wardrobeProvider.wardrobe);

    if (allProducts.isEmpty) {
      return [
        const SliverToBoxAdapter(
          child: EmptyState(
            icon: Icons.search_off,
            title: 'No Products Found',
            message: 'Try adjusting your search or filters',
          ),
        ),
      ];
    }

    return [
      SliverToBoxAdapter(
        child: _buildProductsLayout(allProducts),
      ),
    ];
  }

  Widget _buildProductsLayout(List<dynamic> products) {
    if (_viewMode == 'list') {
      return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return EnhancedProductCard(
            product: product,
            viewMode: 'list',
            onEdit: () => _editProduct(product),
            onViewHistory: () => _viewProductHistory(product),
            onDelete: () => _deleteProduct(product),
          );
        },
      );
    } else {
      return GridView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.65,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return EnhancedProductCard(
            product: product,
            viewMode: 'grid',
            onEdit: () => _editProduct(product),
            onViewHistory: () => _viewProductHistory(product),
            onDelete: () => _deleteProduct(product),
          );
        },
      );
    }
  }

  Map<String, List<dynamic>> _getFilteredWardrobe(Map<String, List<dynamic>> wardrobe) {
    final filtered = <String, List<dynamic>>{};

    for (final entry in wardrobe.entries) {
      final category = entry.key;
      final products = entry.value;

      final filteredProducts = products.where((product) {
        return _matchesSearchQuery(product) && _matchesFilters(product);
      }).toList();

      if (filteredProducts.isNotEmpty) {
        filtered[category] = _sortProducts(filteredProducts);
      }
    }

    return filtered;
  }

  List<dynamic> _getAllFilteredProducts(Map<String, List<dynamic>> wardrobe) {
    final allProducts = <dynamic>[];

    for (final entry in wardrobe.entries) {
      final category = entry.key;
      final products = entry.value;

      if (_selectedCategory == 'All' || _selectedCategory == category) {
        final filteredProducts = products.where((product) {
          return _matchesSearchQuery(product) && _matchesFilters(product);
        }).toList();

        allProducts.addAll(filteredProducts);
      }
    }

    return _sortProducts(allProducts);
  }

  bool _matchesSearchQuery(Map<String, dynamic> product) {
    if (_searchController.text.isEmpty) return true;

    final query = _searchController.text.toLowerCase();
    final name = (product['name'] ?? '').toString().toLowerCase();
    final brand = (product['brand_name'] ?? '').toString().toLowerCase();
    final category = (product['category'] ?? '').toString().toLowerCase();

    return name.contains(query) || brand.contains(query) || category.contains(query);
  }

  bool _matchesFilters(Map<String, dynamic> product) {
    // Add any additional filters here
    return true;
  }

  List<dynamic> _sortProducts(List<dynamic> products) {
    switch (_sortBy) {
      case 'newest':
        products.sort((a, b) {
          final aDate = DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
          final bDate = DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
          return bDate.compareTo(aDate);
        });
        break;
      case 'oldest':
        products.sort((a, b) {
          final aDate = DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
          final bDate = DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
          return aDate.compareTo(bDate);
        });
        break;
      case 'price_high':
        products.sort((a, b) {
          final aPrice = a['rental_cost'] ?? 0;
          final bPrice = b['rental_cost'] ?? 0;
          return bPrice.compareTo(aPrice);
        });
        break;
      case 'price_low':
        products.sort((a, b) {
          final aPrice = a['rental_cost'] ?? 0;
          final bPrice = b['rental_cost'] ?? 0;
          return aPrice.compareTo(bPrice);
        });
        break;
      case 'name':
        products.sort((a, b) {
          final aName = a['name'] ?? '';
          final bName = b['name'] ?? '';
          return aName.compareTo(bName);
        });
        break;
    }
    return products;
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'shirts':
      case 'shirt':
        return Icons.checkroom;
      case 'pants':
      case 'trousers':
        return Icons.straighten;
      case 'dresses':
      case 'dress':
        return Icons.woman;
      case 'suits':
      case 'suit':
        return Icons.business_center;
      case 'accessories':
        return Icons.watch;
      case 'shoes':
        return Icons.sports_soccer;
      case 'jackets':
      case 'jacket':
        return Icons.ac_unit;
      default:
        return Icons.category;
    }
  }

  void _editProduct(Map<String, dynamic> product) async {
    try {
      // Show loading indicator
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(
            color: AppTheme.accentColor,
          ),
        ),
        barrierDismissible: false,
      );

      // Ensure ProductProvider is available and fetch product data
      final productProvider = Get.find<ProductProvider>();
      await productProvider.fetchProductById(product['id']);

      // Close loading dialog
      Get.back();

      // Navigate to update screen
      Get.toNamed(
        Routes.productUpdate,
        parameters: {'productId': product['id']},
      );
    } catch (e) {
      // Close loading dialog if still open
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // Show error message
      Get.snackbar(
        'Error',
        'Failed to load product for editing. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppTheme.errorColor,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.error_outline,
          color: Colors.white,
        ),
      );
    }
  }

  void _deleteProduct(Map<String, dynamic> product) {
    final productProvider = Get.find<ProductProvider>();
    final wardrobeProvider = Get.find<WardrobeProvider>();

    Get.dialog(
      AlertDialog(
        title: const Text('Delete Product'),
        content: Text(
          'Are you sure you want to delete "${product['name']}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back(); // Close dialog

              // Show loading dialog
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.accentColor,
                  ),
                ),
                barrierDismissible: false,
              );

              try {
                // Set the product ID in provider and delete
                productProvider.productId.value = product['id'];
                final success = await productProvider.deleteProduct();

                // Close loading dialog
                Get.back();

                if (success) {
                  // Remove from local wardrobe data
                  wardrobeProvider.removeProductFromWardrobe(product['category'], product['id']);

                  // Show success message
                  Get.snackbar(
                    'Deleted',
                    '${product['name']} has been deleted successfully',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: AppTheme.successColor,
                    colorText: Colors.white,
                    margin: const EdgeInsets.all(16),
                    borderRadius: 12,
                    icon: const Icon(
                      Icons.check_circle,
                      color: Colors.white,
                    ),
                  );
                } else {
                  // Show error message
                  Get.snackbar(
                    'Error',
                    'Failed to delete product. Please try again.',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: AppTheme.errorColor,
                    colorText: Colors.white,
                    margin: const EdgeInsets.all(16),
                    borderRadius: 12,
                    icon: const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                    ),
                  );
                }
              } catch (e) {
                // Close loading dialog if still open
                if (Get.isDialogOpen ?? false) {
                  Get.back();
                }

                // Show error message
                Get.snackbar(
                  'Error',
                  'Something went wrong. Please try again.',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppTheme.errorColor,
                  colorText: Colors.white,
                  margin: const EdgeInsets.all(16),
                  borderRadius: 12,
                  icon: const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _viewProductHistory(Map<String, dynamic> product) {
    // TODO: Navigate to product history when route is available
    Get.snackbar(
      'Product History',
      'Viewing history for ${product['name']}',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
