import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/bottom_navigation_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/providers/product_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class ProductDetailScreen extends StatefulWidget {
  const ProductDetailScreen({Key? key}) : super(key: key);

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  final productProvider = Get.find<ProductProvider>();
  final authProvider = Get.find<AuthProvider>();
  final homeProvider = Get.find<HomeProvider>();
  final bottomNavProvider = Get.find<BottomNavigationProvider>();

  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _loadProduct();
  }

  void _loadProduct() {
    final productId = Get.parameters['productId'];
    if (productId != null) {
      productProvider.fetchProductById(productId);
    }
  }

  void _openImageGallery(int initialIndex) {
    Get.dialog(
      Dialog(
        insetPadding: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            PhotoViewGallery.builder(
              scrollPhysics: const BouncingScrollPhysics(),
              builder: (BuildContext context, int index) {
                return PhotoViewGalleryPageOptions(
                  imageProvider: NetworkImage(productProvider.productOriginalImages[index]),
                  initialScale: PhotoViewComputedScale.contained,
                  minScale: PhotoViewComputedScale.contained * 0.8,
                  maxScale: PhotoViewComputedScale.covered * 2,
                );
              },
              itemCount: productProvider.productImages.length,
              loadingBuilder: (context, event) => Center(
                child: SizedBox(
                  width: 20.0,
                  height: 20.0,
                  child: CircularProgressIndicator(
                    value: event == null
                        ? 0
                        : event.cumulativeBytesLoaded / event.expectedTotalBytes!,
                  ),
                ),
              ),
              backgroundDecoration: const BoxDecoration(
                color: Colors.black,
              ),
              pageController: PageController(initialPage: initialIndex),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
                onPressed: () => Get.back(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _contactSeller() {
    if (!authProvider.isAuthenticated.value) {
      _showLoginDialog('Please login to contact the seller');
      return;
    }

    final product = productProvider.product;
    if (product['user'] != null) {
      Get.toNamed('/messages', parameters: {
        'userId': product['user']['id'],
        'name': product['user']['name'],
      });
    }
  }

  void _showLoginDialog(String message) {
    Get.dialog(
      AlertDialog(
        title: const Text('Login Required'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.toNamed(Routes.login);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Login'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (productProvider.isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }

        final product = productProvider.product;
        if (product.isEmpty) {
          return const Center(
            child: Text('Product not found'),
          );
        }

        return CustomScrollView(
          slivers: [
            // App bar
            SliverAppBar(
              expandedHeight: 400,
              pinned: true,
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
              leading: IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(204), // 0.8 opacity
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.arrow_back),
                ),
                onPressed: () => Get.back(),
              ),
              actions: [
                // Favorite button
                Obx(() {
                  final isInCart = product['is_in_cart'] != null && product['is_in_cart'] > 0;

                  return IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(204), // 0.8 opacity
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isInCart ? Icons.favorite : Icons.favorite_border,
                        color: isInCart ? AppTheme.accentColor : null,
                      ),
                    ),
                    onPressed: () async {
                      if (!authProvider.isAuthenticated.value) {
                        _showLoginDialog('Please login to add this item to your favorites');
                        return;
                      }

                      final success = await homeProvider.toggleFavorite(
                        product['id'],
                        category: product['category'],
                      );

                      if (!success) {
                        _showLoginDialog('Please login to add this item to your favorites');
                      }
                    },
                  );
                }),

                // Share button
                IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(204), // 0.8 opacity
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.share),
                  ),
                  onPressed: () {
                    // TODO: Implement share functionality
                    Get.snackbar(
                      'Coming Soon',
                      'Share functionality will be available soon!',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: AppTheme.infoColor,
                      colorText: Colors.white,
                      margin: const EdgeInsets.all(16),
                      borderRadius: 12,
                      icon: const Icon(
                        Icons.share,
                        color: Colors.white,
                      ),
                    );
                  },
                ),

                // Edit button (only shown if user is the owner)
                if (authProvider.isAuthenticated.value &&
                    product['user'] != null &&
                    product['user']['id'] == authProvider.user['id'])
                  IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(204), // 0.8 opacity
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.edit),
                    ),
                    onPressed: () => Get.toNamed(Routes.productUpdate),
                  ),

                const SizedBox(width: 8),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Stack(
                  children: [
                    // Image carousel
                    PageView.builder(
                      controller: _pageController,
                      itemCount: productProvider.productImages.length,
                      onPageChanged: productProvider.setCurrentImageIndex,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () => _openImageGallery(index),
                          child: CachedNetworkImage(
                            imageUrl: productProvider.productImages[index],
                            fit: BoxFit.cover,
                            placeholder: (context, url) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                            errorWidget: (context, url, error) => const Center(
                              child: Icon(Icons.error),
                            ),
                          ),
                        );
                      },
                    ),

                    // Page indicator
                    Positioned(
                      bottom: 16,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          productProvider.productImages.length,
                          (index) => Obx(() {
                            return Container(
                              width: productProvider.currentImageIndex.value == index ? 16 : 8,
                              height: 8,
                              margin: const EdgeInsets.symmetric(horizontal: 2),
                              decoration: BoxDecoration(
                                color: productProvider.currentImageIndex.value == index
                                    ? AppTheme.accentColor
                                    : Colors.white.withAlpha(128), // 0.5 opacity
                                borderRadius: BorderRadius.circular(4),
                              ),
                            );
                          }),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Product details
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    Text(
                      product['name'],
                      style: AppTheme.textTheme.headlineSmall,
                    ),

                    const SizedBox(height: 8),

                    // Category
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryLightColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        product['category'],
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.accentColor,
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Product info (prices hidden for users)
                    Row(
                      children: [
                        // Availability status
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: AppTheme.successColor,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Available for Rent',
                                style: AppTheme.textTheme.titleMedium?.copyWith(
                                  color: AppTheme.successColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Purchase Cost (if available)
                    if (product['purchase_cost'] != null && product['purchase_cost'] > 0) ...[
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.accentColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.shopping_bag_outlined,
                                  color: AppTheme.accentColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Purchase Price: ₹${product['purchase_cost']}',
                                  style: AppTheme.textTheme.titleMedium?.copyWith(
                                    color: AppTheme.accentColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Rating and reviews
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${product['rating'] ?? 4.5}',
                          style: AppTheme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '(${product['reviews_count'] ?? 0} reviews)',
                          style: AppTheme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textMedium,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Divider
                    const Divider(),

                    const SizedBox(height: 16),

                    // Seller information
                    if (product['user'] != null) ...[
                      Text(
                        'Seller Information',
                        style: AppTheme.textTheme.titleMedium,
                      ),

                      const SizedBox(height: 8),

                      Row(
                        children: [
                          // Seller avatar
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppTheme.backgroundLight,
                              image: product['user']['profile'] != null &&
                                      product['user']['profile']['profile_image'] != null
                                  ? DecorationImage(
                                      image: NetworkImage(
                                        product['user']['profile']['profile_image'],
                                      ),
                                      fit: BoxFit.cover,
                                    )
                                  : null,
                            ),
                            child: product['user']['profile'] == null ||
                                    product['user']['profile']['profile_image'] == null
                                ? const Icon(
                                    Icons.person,
                                    color: AppTheme.textMedium,
                                  )
                                : null,
                          ),

                          const SizedBox(width: 16),

                          // Seller name and location
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  product['user']['name'],
                                  style: AppTheme.textTheme.titleMedium,
                                ),

                                if (product['user']['profile'] != null &&
                                    product['user']['profile']['city'] != null)
                                  Text(
                                    product['user']['profile']['city'],
                                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                                      color: AppTheme.textMedium,
                                    ),
                                  ),
                              ],
                            ),
                          ),

                          // Contact button
                          ElevatedButton(
                            onPressed: _contactSeller,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.accentColor,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Contact'),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      const Divider(),

                      const SizedBox(height: 16),
                    ],

                    // Additional details
                    Text(
                      'Additional Details',
                      style: AppTheme.textTheme.titleMedium,
                    ),

                    const SizedBox(height: 8),

                    // Rental days
                    _buildDetailRow(
                      icon: Icons.calendar_today,
                      title: 'Rental Days',
                      value: '${product['rental_days']} days',
                    ),

                    // Brand
                    if (product['brand_name'] != null)
                      _buildDetailRow(
                        icon: Icons.branding_watermark,
                        title: 'Brand',
                        value: product['brand_name'],
                      ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ],
        );
      }),

      // Bottom bar with action buttons
      // bottomNavigationBar: Obx(() {
      //   if (productProvider.isLoading.value) {
      //     return const SizedBox.shrink();
      //   }

      //   final product = productProvider.product;
      //   if (product.isEmpty) {
      //     return const SizedBox.shrink();
      //   }

      //   return Container(
      //     padding: const EdgeInsets.all(16),
      //     decoration: BoxDecoration(
      //       color: Theme.of(context).scaffoldBackgroundColor,
      //       boxShadow: [
      //         BoxShadow(
      //           color: Colors.black.withAlpha(13), // 0.05 opacity
      //           blurRadius: 10,
      //           offset: const Offset(0, -5),
      //         ),
      //       ],
      //     ),
      //     child: Row(
      //       children: [
      //         // Favorite button
      //         Obx(() {
      //           if (!authProvider.isAuthenticated.value) {
      //             return IconButton(
      //               onPressed: () => bottomNavProvider.changeTabIndex(4),
      //               icon: const Icon(Icons.favorite_border),
      //             );
      //           }

      //           final isInCart = product['is_in_cart'] != null && product['is_in_cart'] > 0;

      //           return IconButton(
      //             onPressed: () => homeProvider.toggleFavorite(
      //               product['id'],
      //               category: product['category'],
      //             ),
      //             icon: Icon(
      //               isInCart ? Icons.favorite : Icons.favorite_border,
      //               color: isInCart ? AppTheme.accentColor : null,
      //             ),
      //           );
      //         }),

      //         const SizedBox(width: 16),

      //         // Contact seller button
      //         // Expanded(
      //         //   child: ElevatedButton(
      //         //     onPressed: _contactSeller,
      //         //     style: ElevatedButton.styleFrom(
      //         //       backgroundColor: AppTheme.accentColor,
      //         //       foregroundColor: Colors.white,
      //         //       padding: const EdgeInsets.symmetric(vertical: 12),
      //         //     ),
      //         //     child: const Text('Contact Seller'),
      //         //   ),
      //         // ),
      //       ],
      //     ),
      //   );
      // }),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppTheme.textMedium,
          ),
          const SizedBox(width: 8),
          Text(
            '$title:',
            style: AppTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textMedium,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: AppTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
