import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart' as cropper;
import 'package:image_picker/image_picker.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/providers/product_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class ProductUploadScreen extends StatefulWidget {
  const ProductUploadScreen({Key? key}) : super(key: key);

  @override
  State<ProductUploadScreen> createState() => _ProductUploadScreenState();
}

class _ProductUploadScreenState extends State<ProductUploadScreen> {
  final productProvider = Get.find<ProductProvider>();
  final categoryProvider = Get.find<CategoryProvider>();
  
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _rentalCostController = TextEditingController();
  final _purchaseCostController = TextEditingController();
  final _securityDepositController = TextEditingController();
  final _rentalDaysController = TextEditingController();
  
  final RxString _selectedCategory = ''.obs;
  final RxList<File> _selectedImages = <File>[].obs;
  final RxInt _thumbnailIndex = 0.obs;
  
  @override
  void initState() {
    super.initState();
    // Set default rental days
    _rentalDaysController.text = '7';
  }
  
  Future<cropper.CroppedFile?> _cropImage(String filePath) async {
    final croppedFile = await cropper.ImageCropper().cropImage(
      sourcePath: filePath,
      uiSettings: [
        cropper.AndroidUiSettings(
            toolbarTitle: 'Crop Image',
            toolbarColor: AppTheme.primaryColor,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: cropper.CropAspectRatioPreset.original,
            lockAspectRatio: false),
        cropper.IOSUiSettings(
          title: 'Crop Image',
        ),
      ],
    );
    return croppedFile;
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile> images = await picker.pickMultiImage();
    
    if (images.isNotEmpty) {
      for (var imageFile in images) {
        final croppedFile = await _cropImage(imageFile.path);
        if (croppedFile != null) {
          _selectedImages.add(File(croppedFile.path));
        }
      }
    }
  }
  
  Future<void> _takePicture() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);
    
    if (image != null) {
      final croppedFile = await _cropImage(image.path);
      if (croppedFile != null) {
        _selectedImages.add(File(croppedFile.path));
      }
    }
  }
  
  void _removeImage(int index) {
    _selectedImages.removeAt(index);
    if (_thumbnailIndex.value >= _selectedImages.length) {
      _thumbnailIndex.value = _selectedImages.length - 1;
    }
    if (_thumbnailIndex.value < 0) {
      _thumbnailIndex.value = 0;
    }
  }
  
  void _setThumbnail(int index) {
    _thumbnailIndex.value = index;
  }
  
  Future<void> _uploadProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    if (_selectedImages.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one image',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.image_not_supported,
          color: Colors.white,
        ),
      );
      return;
    }
    
    if (_selectedCategory.value.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select a category',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.category,
          color: Colors.white,
        ),
      );
      return;
    }
    
    final productData = {
      'name': _nameController.text,
      'brand_name': _brandController.text,
      'category': _selectedCategory.value,
      'rental_cost': int.parse(_rentalCostController.text),
      'purchase_cost': _purchaseCostController.text.isNotEmpty
          ? int.parse(_purchaseCostController.text)
          : 0,
      'security_deposit_cost': int.parse(_securityDepositController.text),
      'rental_days': int.parse(_rentalDaysController.text),
      'thumbnail_index': _thumbnailIndex.value,
    };
    
    final success = await productProvider.uploadProduct(productData, _selectedImages);
    
    if (success) {
      Get.offAllNamed(Routes.wardrobe);
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _rentalCostController.dispose();
    _purchaseCostController.dispose();
    _securityDepositController.dispose();
    _rentalDaysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Product'),
      ),
      body: Obx(() {
        if (productProvider.isUploading.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LoadingIndicator(),
                SizedBox(height: 16),
                Text('Uploading product...'),
              ],
            ),
          );
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product images
                Text(
                  'Product Images',
                  style: AppTheme.textTheme.titleMedium,
                ),
                
                const SizedBox(height: 8),
                
                Container(
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Obx(() {
                    if (_selectedImages.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.image,
                              color: AppTheme.textLight,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No images selected',
                              style: AppTheme.textTheme.bodyMedium?.copyWith(
                                color: AppTheme.textLight,
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    
                    return ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: _selectedImages.length + 1,
                      itemBuilder: (context, index) {
                        if (index == _selectedImages.length) {
                          return _buildAddImageButton();
                        }
                        
                        return _buildImageItem(index);
                      },
                    );
                  }),
                ),
                
                const SizedBox(height: 16),
                
                // Add image buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _pickImages,
                        icon: const Icon(Icons.photo_library),
                        label: const Text('Gallery'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: AppTheme.textDark,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _takePicture,
                        icon: const Icon(Icons.camera_alt),
                        label: const Text('Camera'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: AppTheme.textDark,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Product name
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Product Name',
                    prefixIcon: Icon(Icons.shopping_bag),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter product name';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Brand name
                TextFormField(
                  controller: _brandController,
                  decoration: const InputDecoration(
                    labelText: 'Brand Name (Optional)',
                    prefixIcon: Icon(Icons.branding_watermark),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Category
                GestureDetector(
                  onTap: () => _showCategorySelector(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.category,
                          color: AppTheme.textMedium,
                        ),
                        const SizedBox(width: 16),
                        Obx(() {
                          return Text(
                            _selectedCategory.value.isEmpty
                                ? 'Select Category'
                                : _selectedCategory.value,
                            style: AppTheme.textTheme.bodyMedium?.copyWith(
                              color: _selectedCategory.value.isEmpty
                                  ? AppTheme.textLight
                                  : AppTheme.textDark,
                            ),
                          );
                        }),
                        const Spacer(),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: AppTheme.textMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Rental cost
                TextFormField(
                  controller: _rentalCostController,
                  decoration: const InputDecoration(
                    labelText: 'Rental Cost (₹/day)',
                    prefixIcon: Icon(Icons.currency_rupee),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter rental cost';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Purchase cost
                TextFormField(
                  controller: _purchaseCostController,
                  decoration: const InputDecoration(
                    labelText: 'Purchase Cost (Optional)',
                    prefixIcon: Icon(Icons.currency_rupee),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (int.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Security deposit
                TextFormField(
                  controller: _securityDepositController,
                  decoration: const InputDecoration(
                    labelText: 'Security Deposit',
                    prefixIcon: Icon(Icons.security),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter security deposit';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                // Rental days
                TextFormField(
                  controller: _rentalDaysController,
                  decoration: const InputDecoration(
                    labelText: 'Rental Days',
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter rental days';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 32),
                
                // Upload button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _uploadProduct,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Upload Product'),
                  ),
                ),
                
                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      }),
    );
  }
  
  Widget _buildAddImageButton() {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: AppTheme.backgroundLight,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.borderColor,
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: _pickImages,
        icon: const Icon(
          Icons.add_photo_alternate,
          color: AppTheme.textMedium,
        ),
      ),
    );
  }
  
  Widget _buildImageItem(int index) {
    final isSelected = _thumbnailIndex.value == index;
    
    return Stack(
      children: [
        Container(
          width: 100,
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(
                    color: AppTheme.accentColor,
                    width: 2,
                  )
                : null,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              _selectedImages[index],
              fit: BoxFit.cover,
            ),
          ),
        ),
        
        // Remove button
        Positioned(
          top: 4,
          right: 12,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
        ),
        
        // Thumbnail indicator
        Positioned(
          bottom: 4,
          left: 4,
          child: GestureDetector(
            onTap: () => _setThumbnail(index),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.accentColor
                    : Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                isSelected ? 'Thumbnail' : 'Set as Thumbnail',
                style: AppTheme.textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontSize: 8,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  void _showCategorySelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Text(
                      'Select Category',
                      style: AppTheme.textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: categoryProvider.categories.length,
                  itemBuilder: (context, index) {
                    final category = categoryProvider.categories[index];
                    return ListTile(
                      leading: Image.asset(
                        'assets/images/${category['image']}',
                        width: 40,
                        height: 40,
                      ),
                      title: Text(category['name']),
                      trailing: _selectedCategory.value == category['name']
                          ? const Icon(Icons.check, color: AppTheme.accentColor)
                          : null,
                      onTap: () {
                        _selectedCategory.value = category['name'];
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
