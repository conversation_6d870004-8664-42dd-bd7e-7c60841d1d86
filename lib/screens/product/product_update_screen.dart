import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart' as cropper;
import 'package:image_picker/image_picker.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/providers/product_provider.dart';
import 'package:Laradrobe/providers/wardrobe_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ProductUpdateScreen extends StatefulWidget {
  const ProductUpdateScreen({Key? key}) : super(key: key);

  @override
  State<ProductUpdateScreen> createState() => _ProductUpdateScreenState();
}

class _ProductUpdateScreenState extends State<ProductUpdateScreen> {
  final productProvider = Get.find<ProductProvider>();
  final categoryProvider = Get.find<CategoryProvider>();

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _rentalCostController = TextEditingController();
  final _purchaseCostController = TextEditingController();
  final _securityDepositController = TextEditingController();
  final _rentalDaysController = TextEditingController();

  final RxString _selectedCategory = ''.obs;
  final RxList<File> _selectedImages = <File>[].obs;
  final RxBool _isUpdatingImages = false.obs;
  final RxBool _isDeleting = false.obs;

  @override
  void initState() {
    super.initState();
    _loadProductData();
  }

  void _loadProductData() {
    final product = productProvider.product;
    if (product.isEmpty) {
      Get.back();
      return;
    }

    // Fill form fields with product data
    _nameController.text = product['name'] ?? '';
    _brandController.text = product['brand_name'] ?? '';
    _rentalCostController.text = (product['rental_cost'] ?? 0).toString();
    _purchaseCostController.text = (product['purchase_cost'] ?? 0).toString();
    _securityDepositController.text = (product['security_deposit_cost'] ?? 0).toString();
    _rentalDaysController.text = (product['rental_days'] ?? 7).toString();
    _selectedCategory.value = product['category'] ?? '';
  }

  Future<cropper.CroppedFile?> _cropImage(String filePath) async {
    final croppedFile = await cropper.ImageCropper().cropImage(
      sourcePath: filePath,
      uiSettings: [
        cropper.AndroidUiSettings(
          toolbarTitle: 'Crop Image',
          toolbarColor: AppTheme.primaryColor,
          toolbarWidgetColor: Colors.white,
          initAspectRatio: cropper.CropAspectRatioPreset.original,
          lockAspectRatio: false,
        ),
        cropper.IOSUiSettings(
          title: 'Crop Image',
        ),
      ],
    );
    return croppedFile;
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile> images = await picker.pickMultiImage();

    if (images.isNotEmpty) {
      // Show loading dialog while processing images
      Get.dialog(
        const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: AppTheme.accentColor),
              SizedBox(height: 16),
              Text(
                'Processing images...',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      try {
        for (var imageFile in images) {
          final croppedFile = await _cropImage(imageFile.path);
          if (croppedFile != null) {
            _selectedImages.add(File(croppedFile.path));
          }
        }
      } finally {
        // Close loading dialog
        Get.back();
      }
    }
  }

  Future<void> _takePicture() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.camera);

    if (image != null) {
      // Show loading dialog while processing image
      Get.dialog(
        const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: AppTheme.accentColor),
              SizedBox(height: 16),
              Text(
                'Processing image...',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      try {
        final croppedFile = await _cropImage(image.path);
        if (croppedFile != null) {
          _selectedImages.add(File(croppedFile.path));
        }
      } finally {
        // Close loading dialog
        Get.back();
      }
    }
  }

  void _removeImage(int index) {
    _selectedImages.removeAt(index);
  }

  Future<void> _editImage(int index) async {
    final currentImage = _selectedImages[index];

    // Show loading dialog while processing image
    Get.dialog(
      const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(color: AppTheme.accentColor),
            SizedBox(height: 16),
            Text(
              'Opening image editor...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    try {
      final croppedFile = await _cropImage(currentImage.path);
      if (croppedFile != null) {
        _selectedImages[index] = File(croppedFile.path);
      }
    } finally {
      // Close loading dialog
      Get.back();
    }
  }

  Future<void> _updateProductImages() async {
    if (_selectedImages.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one image',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.image_not_supported,
          color: Colors.white,
        ),
      );
      return;
    }

    _isUpdatingImages.value = true;

    try {
      final success = await productProvider.updateProductImages(_selectedImages);

      if (success) {
        // Refresh product data
        await productProvider.fetchProductById(productProvider.productId.value);
        _selectedImages.clear();
        Get.snackbar(
          'Success',
          'Product images updated successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppTheme.successColor,
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
          icon: const Icon(
            Icons.check_circle,
            color: Colors.white,
          ),
        );
      }
    } finally {
      _isUpdatingImages.value = false;
    }
  }

  Future<void> _deleteProductImage(String fileName) async {
    // Show confirmation dialog
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Image'),
        content: const Text('Are you sure you want to delete this image?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    final success = await productProvider.deleteProductImage(fileName);

    if (success) {
      // No need to refresh product data as we've already updated the productImages list in the provider
    }
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCategory.value.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select a category',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.category,
          color: Colors.white,
        ),
      );
      return;
    }

    final productData = {
      'product_id': productProvider.productId.value,
      'name': _nameController.text,
      'brand_name': _brandController.text,
      'category': _selectedCategory.value,
      'rental_cost': int.parse(_rentalCostController.text),
      'purchase_cost': _purchaseCostController.text.isNotEmpty
          ? int.parse(_purchaseCostController.text)
          : 0,
      'security_deposit_cost': int.parse(_securityDepositController.text),
      'rental_days': int.parse(_rentalDaysController.text),
    };

    final success = await productProvider.updateProduct(productData);

    if (success) {
      // Refresh product data
      await productProvider.fetchProductById(productProvider.productId.value);
      Get.back();
    }
  }

  Future<void> _showDeleteConfirmation() async {
    final product = productProvider.product;
    final productName = product['name'] ?? 'this product';

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Product'),
        content: Text(
          'Are you sure you want to delete "$productName"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteProduct();
    }
  }

  Future<void> _deleteProduct() async {
    final product = productProvider.product;

    // Set loading state
    _isDeleting.value = true;

    try {
      final success = await productProvider.deleteProduct();

      // Stop loading
      _isDeleting.value = false;

      if (success) {
        // Update wardrobe provider if available
        try {
          final wardrobeProvider = Get.find<WardrobeProvider>();
          wardrobeProvider.removeProductFromWardrobe(
            product['category'] ?? '',
            product['id'] ?? ''
          );
        } catch (e) {
          // WardrobeProvider might not be available, that's okay
        }

        // Show success message
        Get.snackbar(
          'Deleted',
          'Product has been deleted successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppTheme.successColor,
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
          icon: const Icon(
            Icons.check_circle,
            color: Colors.white,
          ),
        );

        // Navigate back to previous screen after a short delay
        Future.delayed(const Duration(milliseconds: 1000), () {
          Get.back();
        });
      } else {
        // Show error message
        Get.snackbar(
          'Error',
          'Failed to delete product. Please try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppTheme.errorColor,
          colorText: Colors.white,
          margin: const EdgeInsets.all(16),
          borderRadius: 12,
          icon: const Icon(
            Icons.error_outline,
            color: Colors.white,
          ),
        );
      }
    } catch (e) {
      // Stop loading
      _isDeleting.value = false;

      // Show error message
      Get.snackbar(
        'Error',
        'Something went wrong. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppTheme.errorColor,
        colorText: Colors.white,
        margin: const EdgeInsets.all(16),
        borderRadius: 12,
        icon: const Icon(
          Icons.error_outline,
          color: Colors.white,
        ),
      );
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _rentalCostController.dispose();
    _purchaseCostController.dispose();
    _securityDepositController.dispose();
    _rentalDaysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Update Product'),
        actions: [
          IconButton(
            onPressed: _showDeleteConfirmation,
            icon: const Icon(Icons.delete_outline),
            tooltip: 'Delete Product',
            color: AppTheme.errorColor,
          ),
        ],
      ),
      body: Obx(() {
        if (productProvider.isUpdating.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LoadingIndicator(),
                SizedBox(height: 16),
                Text('Updating product...'),
              ],
            ),
          );
        }

        if (_isDeleting.value) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: AppTheme.accentColor,
                ),
                SizedBox(height: 16),
                Text('Deleting product...'),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Product Images
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Current Images',
                      style: AppTheme.textTheme.titleMedium,
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                Obx(() {
                  return Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: productProvider.productImages.isEmpty
                        ? Center(
                            child: Text(
                              'No images available',
                              style: AppTheme.textTheme.bodySmall?.copyWith(
                                color: AppTheme.textLight,
                              ),
                            ),
                          )
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: productProvider.productImages.length,
                            itemBuilder: (context, index) {
                              final imageUrl = productProvider.productImages[index];
                              // Extract file name from URL
                              final fileName = imageUrl.split('/').last;

                              return Stack(
                                children: [
                                  Container(
                                    width: 100,
                                    margin: const EdgeInsets.only(right: 8),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: CachedNetworkImage(
                                        imageUrl: imageUrl,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => const Center(
                                          child: CircularProgressIndicator(),
                                        ),
                                        errorWidget: (context, url, error) => const Center(
                                          child: Icon(Icons.error),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 8,
                                    child: GestureDetector(
                                      onTap: () => _deleteProductImage(fileName),
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                  );
                }),

                const SizedBox(height: 24),

                // New Images Section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Update Images',
                          style: AppTheme.textTheme.titleMedium,
                        ),
                        Text(
                          'Add new images with crop & edit options',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textMedium,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        IconButton(
                          onPressed: _pickImages,
                          icon: const Icon(Icons.photo_library),
                          tooltip: 'Select from gallery',
                        ),
                        IconButton(
                          onPressed: _takePicture,
                          icon: const Icon(Icons.camera_alt),
                          tooltip: 'Take a picture',
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                Obx(() {
                  if (_selectedImages.isEmpty) {
                    return Container(
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundLight,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppTheme.borderColor,
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.add_photo_alternate,
                              size: 40,
                              color: AppTheme.textLight,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Tap the icons above to add images',
                              style: AppTheme.textTheme.bodySmall?.copyWith(
                                color: AppTheme.textLight,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Images will be automatically cropped',
                              style: AppTheme.textTheme.bodySmall?.copyWith(
                                color: AppTheme.textLight,
                                fontSize: 11,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  } else {
                    return Column(
                      children: [
                        Container(
                          height: 120,
                          decoration: BoxDecoration(
                            color: AppTheme.backgroundLight,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _selectedImages.length,
                            itemBuilder: (context, index) {
                              return Stack(
                                children: [
                                  Container(
                                    width: 100,
                                    margin: const EdgeInsets.only(right: 8),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.file(
                                        _selectedImages[index],
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),

                                  // Remove button
                                  Positioned(
                                    top: 0,
                                    right: 8,
                                    child: GestureDetector(
                                      onTap: () => _removeImage(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Colors.red,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.close,
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Edit/Crop button
                                  Positioned(
                                    bottom: 0,
                                    right: 8,
                                    child: GestureDetector(
                                      onTap: () => _editImage(index),
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: AppTheme.accentColor,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.edit,
                                          size: 16,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tap ✏️ to edit/crop • Tap ❌ to remove',
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textMedium,
                            fontSize: 11,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: Obx(() {
                            return ElevatedButton(
                              onPressed: _isUpdatingImages.value
                                  ? null
                                  : _updateProductImages,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.accentColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: _isUpdatingImages.value
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Text('Update Images'),
                            );
                          }),
                        ),
                      ],
                    );
                  }
                }),

                const SizedBox(height: 24),

                // Product name
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Product Name',
                    prefixIcon: Icon(Icons.shopping_bag),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter product name';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Brand name
                TextFormField(
                  controller: _brandController,
                  decoration: const InputDecoration(
                    labelText: 'Brand Name (Optional)',
                    prefixIcon: Icon(Icons.branding_watermark),
                  ),
                ),

                const SizedBox(height: 16),

                // Category
                GestureDetector(
                  onTap: () => _showCategorySelector(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.category,
                          color: AppTheme.textMedium,
                        ),
                        const SizedBox(width: 16),
                        Obx(() {
                          return Text(
                            _selectedCategory.value.isEmpty
                                ? 'Select Category'
                                : _selectedCategory.value,
                            style: AppTheme.textTheme.bodyMedium?.copyWith(
                              color: _selectedCategory.value.isEmpty
                                  ? AppTheme.textLight
                                  : AppTheme.textDark,
                            ),
                          );
                        }),
                        const Spacer(),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: AppTheme.textMedium,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Rental cost
                TextFormField(
                  controller: _rentalCostController,
                  decoration: const InputDecoration(
                    labelText: 'Rental Cost (₹/day)',
                    prefixIcon: Icon(Icons.currency_rupee),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter rental cost';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Purchase cost
                TextFormField(
                  controller: _purchaseCostController,
                  decoration: const InputDecoration(
                    labelText: 'Purchase Cost (Optional)',
                    prefixIcon: Icon(Icons.currency_rupee),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (int.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Security deposit
                TextFormField(
                  controller: _securityDepositController,
                  decoration: const InputDecoration(
                    labelText: 'Security Deposit',
                    prefixIcon: Icon(Icons.security),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter security deposit';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Rental days
                TextFormField(
                  controller: _rentalDaysController,
                  decoration: const InputDecoration(
                    labelText: 'Rental Days',
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter rental days';
                    }
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 32),

                // Update button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _updateProduct,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Update Product'),
                  ),
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      }),
    );
  }

  void _showCategorySelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Text(
                      'Select Category',
                      style: AppTheme.textTheme.titleLarge,
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: categoryProvider.categories.length,
                  itemBuilder: (context, index) {
                    final category = categoryProvider.categories[index];
                    return ListTile(
                      leading: Image.asset(
                        'assets/images/${category['image']}',
                        width: 40,
                        height: 40,
                      ),
                      title: Text(category['name']),
                      trailing: _selectedCategory.value == category['name']
                          ? const Icon(Icons.check, color: AppTheme.accentColor)
                          : null,
                      onTap: () {
                        _selectedCategory.value = category['name'];
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
