import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/models/order.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/utils/common.dart';
import 'package:Laradrobe/widgets/custom_app_bar.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';
import 'package:Laradrobe/widgets/network_image.dart';

class OrderScreen extends StatefulWidget {
  const OrderScreen({Key? key}) : super(key: key);

  @override
  State<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends State<OrderScreen> with SingleTickerProviderStateMixin {
  final RxList<Order> _orders = <Order>[].obs;
  final RxBool _isLoading = false.obs;
  
  late TabController _tabController;
  final List<String> _tabs = ['All', 'Active', 'Completed', 'Cancelled'];
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _fetchOrders();
  }
  
  Future<void> _fetchOrders() async {
    _isLoading.value = true;
    
    try {
      final orders = await ApiService.getOrders();
      _orders.value = orders;
    } catch (e) {
      print('Error fetching orders: $e');
      Get.snackbar(
        'Error',
        'Failed to load orders. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }
  
  List<Order> _getFilteredOrders(String status) {
    if (status == 'All') {
      return _orders;
    }
    
    return _orders.where((order) {
      if (status == 'Active') {
        return order.status == 'active' || order.status == 'pending';
      } else if (status == 'Completed') {
        return order.status == 'completed';
      } else if (status == 'Cancelled') {
        return order.status == 'cancelled';
      }
      return false;
    }).toList();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'My Orders',
        bottom: TabBar(
          controller: _tabController,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
          labelColor: AppTheme.accentColor,
          unselectedLabelColor: AppTheme.textMedium,
          indicatorColor: AppTheme.accentColor,
          indicatorWeight: 3,
        ),
      ),
      body: Obx(() {
        if (_isLoading.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }
        
        if (_orders.isEmpty) {
          return EmptyState(
            icon: Icons.shopping_bag_outlined,
            title: 'No Orders Yet',
            message: 'You haven\'t placed any orders yet. Start shopping to see your orders here.',
            buttonText: 'Browse Products',
            onButtonPressed: () => Get.offAllNamed(Routes.home),
          );
        }
        
        return TabBarView(
          controller: _tabController,
          children: _tabs.map((tab) {
            final filteredOrders = _getFilteredOrders(tab);
            
            if (filteredOrders.isEmpty) {
              return EmptyState(
                icon: Icons.shopping_bag_outlined,
                title: 'No $tab Orders',
                message: 'You don\'t have any $tab orders at the moment.',
                buttonText: tab == 'All' ? 'Browse Products' : 'View All Orders',
                onButtonPressed: () {
                  if (tab == 'All') {
                    Get.offAllNamed(Routes.home);
                  } else {
                    _tabController.animateTo(0);
                  }
                },
              );
            }
            
            return RefreshIndicator(
              onRefresh: _fetchOrders,
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: filteredOrders.length,
                itemBuilder: (context, index) {
                  final order = filteredOrders[index];
                  return _buildOrderCard(order);
                },
              ),
            );
          }).toList(),
        );
      }),
    );
  }
  
  Widget _buildOrderCard(Order order) {
    final product = order.product;
    final orderDate = DateFormat('MMM d, yyyy').format(order.createdAt);
    
    // Get status color
    Color statusColor;
    switch (order.status?.toLowerCase()) {
      case 'active':
        statusColor = AppTheme.infoColor;
        break;
      case 'pending':
        statusColor = Colors.amber;
        break;
      case 'completed':
        statusColor = AppTheme.successColor;
        break;
      case 'cancelled':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = AppTheme.textLight;
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Order ID
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Order #${order.id.substring(0, 8)}',
                        style: AppTheme.textTheme.titleMedium,
                      ),
                      Text(
                        orderDate,
                        style: AppTheme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Order status
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    order.status?.capitalize ?? 'Unknown',
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Product details
          InkWell(
            onTap: () => Get.toNamed(
              Routes.productDetail,
              parameters: {'productId': product.id},
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Product image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CustomNetworkImage(
                      imageUrl: '${ApiService.productImageUrl}${product.thumbnail}',
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Product details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name,
                          style: AppTheme.textTheme.titleMedium,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: 4),
                        
                        Text(
                          product.category,
                          style: AppTheme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.textMedium,
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        Text(
                          formatCurrency(product.rentalCost),
                          style: AppTheme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.accentColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const Divider(height: 1),
          
          // Order details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Rental period
                Row(
                  children: [
                    const Icon(
                      Icons.date_range,
                      size: 16,
                      color: AppTheme.textMedium,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Rental Period:',
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textMedium,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${order.from} to ${order.to}',
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Purpose
                Row(
                  children: [
                    const Icon(
                      Icons.event,
                      size: 16,
                      color: AppTheme.textMedium,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Purpose:',
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textMedium,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      order.purpose,
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // Total amount
                Row(
                  children: [
                    const Icon(
                      Icons.payments,
                      size: 16,
                      color: AppTheme.textMedium,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Total Amount:',
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textMedium,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      formatCurrency(order.rentalCost + order.securityDepositCost),
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Action buttons
          if (order.status?.toLowerCase() == 'active' || order.status?.toLowerCase() == 'pending')
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        // TODO: Implement cancel order functionality
                        Get.snackbar(
                          'Coming Soon',
                          'Cancel order functionality will be available soon!',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.errorColor,
                        side: BorderSide(color: AppTheme.errorColor),
                      ),
                      child: const Text('Cancel Order'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: Implement contact seller functionality
                        Get.snackbar(
                          'Coming Soon',
                          'Contact seller functionality will be available soon!',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.accentColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Contact Seller'),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
