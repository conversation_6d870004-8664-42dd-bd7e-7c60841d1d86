import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/config/routes.dart'; // Added import for Routes
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Get.find<AuthProvider>();
    
    return Scaffold(
      body: SafeArea(
        child: Obx(() {
          if (authProvider.isLoading.value) {
            return const Center(
              child: LoadingIndicator(),
            );
          }
          
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),
                  
                  // Logo
                  Image.asset(
                    'assets/images/logo.png',
                    height: 80,
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Welcome text
                  Text(
                    'Welcome to Laradrobe',
                    style: AppTheme.textTheme.headlineMedium,
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    'Sign in to continue',
                    style: AppTheme.textTheme.bodyLarge?.copyWith(
                      color: AppTheme.textMedium,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 60),
                  
                  // Social login buttons
                  _buildSocialLoginButton(
                    context: context,
                    text: 'Continue with Google',
                    icon: 'assets/images/google_logo.png',
                    onPressed: () => authProvider.signInWithGoogle(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // _buildSocialLoginButton(
                  //   context: context,
                  //   text: 'Continue with Facebook',
                  //   icon: 'assets/images/facebook_logo.png',
                  //   onPressed: () => authProvider.signInWithFacebook(),
                  // ),
                  
                  // const SizedBox(height: 16),
                  
                  // _buildSocialLoginButton(
                  //   context: context,
                  //   text: 'Continue with Twitter',
                  //   icon: 'assets/images/twitter_logo.png',
                  //   onPressed: () => authProvider.signInWithTwitter(),
                  // ),
                  
                  const SizedBox(height: 40),
                  
                  // Skip login
                  TextButton(
                    onPressed: () => Get.offAllNamed(Routes.main), // Changed to navigate to main screen
                    child: Text(
                      'Skip for now',
                      style: AppTheme.textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textMedium,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Terms and privacy
                  Text(
                    'By continuing, you agree to our Terms of Service and Privacy Policy',
                    style: AppTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textLight,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
  
  Widget _buildSocialLoginButton({
    required BuildContext context,
    required String text,
    required String icon,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: Theme.of(context).dividerColor),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              icon,
              height: 24,
              width: 24,
            ),
            const SizedBox(width: 12),
            Text(
              text,
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
