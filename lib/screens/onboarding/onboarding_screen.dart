import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/utils/storage_helper.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  final RxInt _currentPage = 0.obs;
  
  final List<Map<String, dynamic>> _onboardingData = [
    {
      'title': 'Rent Designer Clothes',
      'description': 'Access premium fashion without the premium price tag',
      'image': 'assets/images/onboarding_1.png',
    },
    {
      'title': 'Share Your Wardrobe',
      'description': 'Earn by renting out your unused clothes',
      'image': 'assets/images/onboarding_2.png',
    },
    {
      'title': 'Sustainable Fashion',
      'description': 'Reduce waste and contribute to a greener planet',
      'image': 'assets/images/onboarding_3.png',
    },
  ];
  
  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }
  
  Future<void> _checkOnboardingStatus() async {
    final isCompleted = await StorageHelper.isOnboardingCompleted();
    final authProvider = Get.find<AuthProvider>();
    
    if (isCompleted) {
      if (authProvider.isAuthenticated.value) {
        Get.offAllNamed(Routes.main);
      } else {
        Get.offAllNamed(Routes.login);
      }
    }
  }
  
  void _onPageChanged(int page) {
    _currentPage.value = page;
  }
  
  void _nextPage() {
    if (_currentPage.value < _onboardingData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }
  
  void _skipOnboarding() {
    _completeOnboarding();
  }
  
  Future<void> _completeOnboarding() async {
    await StorageHelper.setOnboardingCompleted(true);
    Get.offAllNamed(Routes.login);
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: _skipOnboarding,
                  child: Text(
                    'Skip',
                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.accentColor,
                    ),
                  ),
                ),
              ),
            ),
            
            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _onboardingData.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Image
                        Image.asset(
                          _onboardingData[index]['image'],
                          height: 300,
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Title
                        Text(
                          _onboardingData[index]['title'],
                          style: AppTheme.textTheme.headlineMedium,
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Description
                        Text(
                          _onboardingData[index]['description'],
                          style: AppTheme.textTheme.bodyLarge?.copyWith(
                            color: AppTheme.textMedium,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            
            // Indicators and button
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Row(
                children: [
                  // Page indicators
                  Row(
                    children: List.generate(
                      _onboardingData.length,
                      (index) => Obx(() {
                        return Container(
                          width: _currentPage.value == index ? 20 : 8,
                          height: 8,
                          margin: const EdgeInsets.only(right: 4),
                          decoration: BoxDecoration(
                            color: _currentPage.value == index
                                ? AppTheme.accentColor
                                : AppTheme.primaryLightColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        );
                      }),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Next/Get Started button
                  Obx(() {
                    final isLastPage = _currentPage.value == _onboardingData.length - 1;
                    
                    return ElevatedButton(
                      onPressed: _nextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        isLastPage ? 'Get Started' : 'Next',
                        style: AppTheme.textTheme.labelLarge?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
