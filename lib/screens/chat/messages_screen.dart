import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/conversation_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class MessagesScreen extends StatefulWidget {
  const MessagesScreen({Key? key}) : super(key: key);

  @override
  State<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends State<MessagesScreen> {
  final authProvider = Get.find<AuthProvider>();
  final conversationProvider = Get.find<ConversationProvider>();
  
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  late String _conversationId;
  late String _recipientName;
  
  @override
  void initState() {
    super.initState();
    _initializeConversation();
  }
  
  void _initializeConversation() async {
    // Get conversation ID from parameters
    _conversationId = Get.parameters['conversationId'] ?? '';
    _recipientName = Get.parameters['name'] ?? 'Chat';
    
    // If we have a user ID but no conversation ID, get the conversation ID
    final userId = Get.parameters['userId'];
    if (_conversationId.isEmpty && userId != null) {
      _conversationId = await conversationProvider.getConversationId(userId);
    }
    
    // Fetch messages
    if (_conversationId.isNotEmpty) {
      await conversationProvider.fetchMessages(_conversationId);
      
      // Scroll to bottom after messages are loaded
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    }
  }
  
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }
  
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _conversationId.isEmpty) return;
    
    _messageController.clear();
    
    await conversationProvider.sendMessage(_conversationId, message);
    
    // Scroll to bottom after sending message
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }
  
  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_recipientName),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // TODO: Show conversation info
              Get.snackbar(
                'Coming Soon',
                'Conversation info will be available soon!',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: Obx(() {
              if (conversationProvider.isLoadingMessages.value) {
                return const Center(
                  child: LoadingIndicator(),
                );
              }
              
              if (conversationProvider.messages.isEmpty) {
                return Center(
                  child: Text(
                    'No messages yet. Start a conversation!',
                    style: AppTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textMedium,
                    ),
                  ),
                );
              }
              
              return ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: conversationProvider.messages.length,
                itemBuilder: (context, index) {
                  final message = conversationProvider.messages[index];
                  final isCurrentUser = message['user_id'] == authProvider.user['id'];
                  final messageTime = DateTime.parse(message['created_at']);
                  
                  // Show date header if needed
                  final showDateHeader = index == 0 ||
                      _shouldShowDateHeader(
                        messageTime,
                        DateTime.parse(conversationProvider.messages[index - 1]['created_at']),
                      );
                  
                  return Column(
                    children: [
                      if (showDateHeader) _buildDateHeader(messageTime),
                      _buildMessageBubble(message, isCurrentUser),
                    ],
                  );
                },
              );
            }),
          ),
          
          // Message input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Row(
              children: [
                // Attachment button
                IconButton(
                  icon: const Icon(Icons.attach_file),
                  onPressed: () {
                    // TODO: Implement attachment functionality
                    Get.snackbar(
                      'Coming Soon',
                      'Attachment functionality will be available soon!',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  },
                ),
                
                // Message input field
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Type a message...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.backgroundLight,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    textCapitalization: TextCapitalization.sentences,
                    minLines: 1,
                    maxLines: 5,
                  ),
                ),
                
                // Send button
                Obx(() {
                  return IconButton(
                    icon: conversationProvider.isSendingMessage.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          )
                        : const Icon(Icons.send),
                    onPressed: conversationProvider.isSendingMessage.value
                        ? null
                        : _sendMessage,
                    color: AppTheme.accentColor,
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildMessageBubble(Map<String, dynamic> message, bool isCurrentUser) {
    final messageTime = DateTime.parse(message['created_at']);
    final formattedTime = DateFormat.jm().format(messageTime);
    
    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isCurrentUser ? AppTheme.accentColor : AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(16),
        ),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message['content'],
              style: AppTheme.textTheme.bodyMedium?.copyWith(
                color: isCurrentUser ? Colors.white : AppTheme.textDark,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              formattedTime,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: isCurrentUser ? Colors.white.withOpacity(0.7) : AppTheme.textLight,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDateHeader(DateTime messageTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(messageTime.year, messageTime.month, messageTime.day);
    
    String dateText;
    if (messageDate == today) {
      dateText = 'Today';
    } else if (messageDate == yesterday) {
      dateText = 'Yesterday';
    } else {
      dateText = DateFormat.yMMMd().format(messageTime);
    }
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          const Expanded(child: Divider()),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              dateText,
              style: AppTheme.textTheme.bodySmall?.copyWith(
                color: AppTheme.textLight,
              ),
            ),
          ),
          const Expanded(child: Divider()),
        ],
      ),
    );
  }
  
  bool _shouldShowDateHeader(DateTime current, DateTime previous) {
    return current.year != previous.year ||
        current.month != previous.month ||
        current.day != previous.day;
  }
}
