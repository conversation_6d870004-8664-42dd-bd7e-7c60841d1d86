import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/conversation_provider.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/empty_state.dart';
import 'package:Laradrobe/widgets/loading_indicator.dart';

class ConversationScreen extends StatefulWidget {
  const ConversationScreen({Key? key}) : super(key: key);

  @override
  State<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreen> {
  final authProvider = Get.find<AuthProvider>();
  final conversationProvider = Get.find<ConversationProvider>();
  
  @override
  void initState() {
    super.initState();
    // _refreshConversations();
  }
  
  Future<void> _refreshConversations() async {
    if (authProvider.isAuthenticated.value) {
      await conversationProvider.fetchConversations();
      await conversationProvider.getUnseenMessagesCount();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Messages'),
      ),
      body: Obx(() {
        // Check if user is authenticated
        if (!authProvider.isAuthenticated.value) {
          return EmptyState(
            icon: Icons.login,
            title: 'Login Required',
            message: 'Please login to view your messages',
            buttonText: 'Login',
            onButtonPressed: () => Get.toNamed(Routes.login),
          );
        }
        
        // Show loading indicator
        if (conversationProvider.isLoadingConversations.value) {
          return const Center(
            child: LoadingIndicator(),
          );
        }
        
        // Show empty state if no conversations
        if (conversationProvider.conversations.isEmpty) {
          return const EmptyState(
            icon: Icons.chat_bubble_outline,
            title: 'No Messages',
            message: 'You don\'t have any messages yet. Start a conversation by contacting a seller.',
          );
        }
        
        // Show conversations
        return RefreshIndicator(
          onRefresh: _refreshConversations,
          child: ListView.separated(
            itemCount: conversationProvider.conversations.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final conversation = conversationProvider.conversations[index];
              final otherUser = conversation['participants'].firstWhere(
                (user) => user['id'] != authProvider.user['id'],
                orElse: () => {'name': 'Unknown'},
              );
              
              final lastMessageTime = conversation['last_message_time'] != null
                  ? DateTime.parse(conversation['last_message_time'])
                  : null;
              
              final formattedTime = lastMessageTime != null
                  ? _formatTime(lastMessageTime)
                  : '';
              
              final isSeen = conversation['is_seen'] ?? true;
              
              return ListTile(
                leading: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppTheme.backgroundLight,
                    image: otherUser['profile'] != null &&
                            otherUser['profile']['profile_image'] != null
                        ? DecorationImage(
                            image: NetworkImage(
                              otherUser['profile']['profile_image'],
                            ),
                            fit: BoxFit.cover,
                          )
                        : null,
                  ),
                  child: otherUser['profile'] == null ||
                          otherUser['profile']['profile_image'] == null
                      ? const Icon(
                          Icons.person,
                          color: AppTheme.textMedium,
                        )
                      : null,
                ),
                title: Text(
                  otherUser['name'],
                  style: AppTheme.textTheme.titleMedium?.copyWith(
                    fontWeight: isSeen ? FontWeight.normal : FontWeight.bold,
                  ),
                ),
                subtitle: Text(
                  conversation['last_message_content'] ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTheme.textTheme.bodyMedium?.copyWith(
                    color: isSeen ? AppTheme.textMedium : AppTheme.textDark,
                    fontWeight: isSeen ? FontWeight.normal : FontWeight.w500,
                  ),
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      formattedTime,
                      style: AppTheme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.textLight,
                      ),
                    ),
                    const SizedBox(height: 4),
                    if (!isSeen)
                      Container(
                        width: 10,
                        height: 10,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppTheme.accentColor,
                        ),
                      ),
                  ],
                ),
                onTap: () {
                  Get.toNamed(
                    Routes.messages,
                    parameters: {
                      'conversationId': conversation['id'],
                      'name': otherUser['name'],
                    },
                  );
                },
              );
            },
          ),
        );
      }),
    );
  }
  
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    if (messageDate == today) {
      return DateFormat.jm().format(dateTime); // e.g. 2:30 PM
    } else if (messageDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(dateTime).inDays < 7) {
      return DateFormat.E().format(dateTime); // e.g. Mon, Tue
    } else {
      return DateFormat.yMd().format(dateTime); // e.g. 1/1/2021
    }
  }
}
