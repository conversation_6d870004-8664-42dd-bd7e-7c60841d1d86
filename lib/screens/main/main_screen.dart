import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/bottom_navigation_provider.dart';
import 'package:Laradrobe/providers/conversation_provider.dart';
import 'package:Laradrobe/screens/cart/cart_screen.dart';
// import 'package:Laradrobe/screens/chat/conversation_screen.dart';
import 'package:Laradrobe/screens/home/<USER>';
import 'package:Laradrobe/screens/profile/profile_screen.dart';
import 'package:Laradrobe/screens/wardrobe/wardrobe_screen.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/widgets/animated_fab.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({Key? key}) : super(key: key);

  void _showLoginDialog(BuildContext context, int tabIndex) {
    final String tabName = tabIndex == 1
        ? 'Wardrobe'
        : tabIndex == 2
            ? 'Favorites'
            : 'Messages';

    Get.dialog(
      AlertDialog(
        title: const Text('Login Required'),
        content: Text('Please login to access your $tabName'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.toNamed(Routes.login);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Login'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bottomNavProvider = Get.find<BottomNavigationProvider>();
    final conversationProvider = Get.find<ConversationProvider>();
    final authProvider = Get.find<AuthProvider>();

    // Screens to be displayed
    final List<Widget> screens = [
      const HomeScreen(),
      const WardrobeScreen(),
      const CartScreen(),
      // const ConversationScreen(),
      const ProfileScreen(),
    ];

    return Scaffold(
      body: Obx(() => IndexedStack(
        index: bottomNavProvider.tabIndex.value,
        children: screens,
      )),
      bottomNavigationBar: Obx(() => NavigationBar(
        selectedIndex: bottomNavProvider.tabIndex.value,
        onDestinationSelected: (index) {
          // Check if user is trying to access a protected tab
          if ((index == 1 || index == 2 || index == 3) && !authProvider.isAuthenticated.value) {
            // Show login dialog instead of navigating
            _showLoginDialog(context, index);
          } else {
            bottomNavProvider.changeTabIndex(index);
          }
        },
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 8,
        labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
        destinations: [
          const NavigationDestination(
            icon: Icon(Icons.home_outlined),
            selectedIcon: Icon(Icons.home),
            label: 'Home',
          ),
          const NavigationDestination(
            icon: Icon(Icons.checkroom_outlined),
            selectedIcon: Icon(Icons.checkroom),
            label: 'Wardrobe',
          ),
          const NavigationDestination(
            icon: Icon(Icons.favorite_outline),
            selectedIcon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
          // NavigationDestination(
          //   icon: Stack(
          //     children: [
          //       const Icon(Icons.chat_outlined),
          //       Obx(() {
          //         final count = conversationProvider.unseenMessagesCount.value;
          //         return count > 0
          //             ? Positioned(
          //                 right: 0,
          //                 top: 0,
          //                 child: Container(
          //                   padding: const EdgeInsets.all(2),
          //                   decoration: BoxDecoration(
          //                     color: AppTheme.accentColor,
          //                     borderRadius: BorderRadius.circular(10),
          //                   ),
          //                   constraints: const BoxConstraints(
          //                     minWidth: 16,
          //                     minHeight: 16,
          //                   ),
          //                   child: Text(
          //                     count > 9 ? '9+' : count.toString(),
          //                     style: const TextStyle(
          //                       color: Colors.white,
          //                       fontSize: 10,
          //                       fontWeight: FontWeight.bold,
          //                     ),
          //                     textAlign: TextAlign.center,
          //                   ),
          //                 ),
          //               )
          //             : const SizedBox.shrink();
          //       }),
          //     ],
          //   ),
          //   selectedIcon: const Icon(Icons.chat),
          //   label: 'Messages',
          // ),
          const NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      )),
      floatingActionButton: Obx(() {
        if (bottomNavProvider.tabIndex.value == 1) {
          return AnimatedFAB(
            onPressed: () {
              if (!authProvider.isAuthenticated.value) {
                _showLoginDialog(context, 1);
              } else {
                Get.toNamed('/product/upload');
              }
            },
            icon: Icons.add,
            label: 'Add Item',
          );
        } else {
          return const SizedBox.shrink();
        }
      }),
    );
  }
}
