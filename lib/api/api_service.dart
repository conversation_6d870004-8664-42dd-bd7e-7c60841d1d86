import 'dart:async';
import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:Laradrobe/config/env.dart';
import 'package:Laradrobe/models/conversation.dart';
import 'package:Laradrobe/models/message.dart';
import 'package:Laradrobe/models/order.dart';
import 'package:Laradrobe/models/product.dart';
import 'package:Laradrobe/models/referral.dart';
import 'package:Laradrobe/models/user.dart';
import 'package:Laradrobe/utils/storage_helper.dart';

class ApiService {
  static String get baseUrl => dotenv.env[Env.apiBaseUrl] ?? '';
  static String get productImageUrl => dotenv.env[Env.productImageUrl] ?? '';

  // HTTP client with timeout
  static final _client = GetConnect(timeout: const Duration(seconds: 30));

  // Headers with auth token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await StorageHelper.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Handle API response
  static dynamic _handleResponse(Response response) {
    // Accept both 200 (OK) and 201 (Created) as successful responses
    if (response.statusCode == 200 || response.statusCode == 201) {
      return response.body;
    } else if (response.statusCode == 401) {
      // Don't automatically redirect to login for 401 errors
      // Just throw an exception that can be handled by the caller
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed with status code: ${response.statusCode}');
    }
  }

  // Authentication APIs
  static Future<Map<String, dynamic>> googleSignIn(String accessToken, String firebaseToken) async {
    final response = await _client.post(
      '$baseUrl/google-signin',
      {
        'accessToken': accessToken,
        'firebaseToken': firebaseToken,
      },
      headers: await _getAuthHeaders(),
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> facebookSignIn(String accessToken) async {
    final response = await _client.post(
      '$baseUrl/facebook-signin',
      {'accessToken': accessToken},
      headers: await _getAuthHeaders(),
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> twitterSignIn(String accessToken, String accessTokenSecret) async {
    final response = await _client.post(
      '$baseUrl/twitter-signin',
      {
        'accessToken': accessToken,
        'accessTokenSecret': accessTokenSecret,
      },
      headers: await _getAuthHeaders(),
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> verifyToken() async {
    final response = await _client.post(
      '$baseUrl/verify-token',
      {},
      headers: await _getAuthHeaders(),
    );
    return _handleResponse(response);
  }

  static Future<void> logout() async {
    await _client.post(
      '$baseUrl/logout',
      {},
      headers: await _getAuthHeaders(),
    );
  }

  // Home screen APIs
  static Future<List<Map<String, dynamic>>> fetchProductsByCityCategory(String city, String? category, int page) async {
    final response = await _client.get(
      '$baseUrl/home-screen-by-city',
      query: {
        'city': city,
        'category': category ?? '',
        'page': page.toString(),
      },
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data);
  }

  // Category APIs
  static Future<List<Map<String, dynamic>>> getAllCategories() async {
    print('Making API call to: $baseUrl/get-all-categories');

    final response = await _client.get(
      '$baseUrl/get-all-categories',
      headers: await _getAuthHeaders(),
    );

    print('API Response status: ${response.statusCode}');
    print('API Response body: ${response.body}');

    final data = _handleResponse(response);
    print('Processed data: $data');
    print('Data type: ${data.runtimeType}');

    // Handle the case where the API returns a list of strings
    if (data is List) {
      final result = data.map((category) {
        if (category is String) {
          // Convert each string to a map with 'name' key
          return {'name': category};
        } else if (category is Map<String, dynamic>) {
          // If it's already a map, return it as is
          return category;
        } else {
          // Fallback for unexpected data types
          return {'name': category.toString()};
        }
      }).toList().cast<Map<String, dynamic>>();

      print('Converted result: $result');
      print('Result length: ${result.length}');

      return result;
    }

    // Fallback to original implementation if data is not a list
    final result = List<Map<String, dynamic>>.from(data);
    print('Final result: $result');
    print('Result length: ${result.length}');

    return result;
  }

  // Product APIs
  static Future<Map<String, dynamic>> getProductById(String productId) async {
    final response = await _client.get(
      '$baseUrl/product/get',
      query: {'product_id': productId},
      headers: await _getAuthHeaders(),
    );

    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> uploadProduct(Map<String, dynamic> productData, List<dynamic> files) async {
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/upload-product'),
    );

    // Add auth header
    final token = await StorageHelper.getToken();
    if (token != null) {
      request.headers['Authorization'] = 'Bearer $token';
    }

    // Add product data
    productData.forEach((key, value) {
      request.fields[key] = value.toString();
    });

    // Add files
    for (var i = 0; i < files.length; i++) {
      var file = files[i];
      var stream = http.ByteStream(file.openRead());
      var length = await file.length();

      var multipartFile = http.MultipartFile(
        'file[]',
        stream,
        length,
        filename: 'image_$i.jpg',
      );

      request.files.add(multipartFile);
    }

    var response = await request.send();
    var responseData = await response.stream.bytesToString();

    if (response.statusCode == 200) {
      return json.decode(responseData);
    } else {
      throw Exception('Failed to upload product');
    }
  }

  static Future<void> updateProduct(Map<String, dynamic> productData) async {
    final response = await _client.post(
      '$baseUrl/product/update',
      productData,
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  static Future<void> updateProductImages(String productId, List<dynamic> files) async {
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/product/update-image'),
    );

    // Add auth header
    final token = await StorageHelper.getToken();
    if (token != null) {
      request.headers['Authorization'] = 'Bearer $token';
    }

    // Add product_id
    request.fields['product_id'] = productId;

    // Add files
    for (var i = 0; i < files.length; i++) {
      var file = files[i];
      var stream = http.ByteStream(file.openRead());
      var length = await file.length();

      var multipartFile = http.MultipartFile(
        'file[]',
        stream,
        length,
        filename: 'image_$i.jpg',
      );

      request.files.add(multipartFile);
    }

    var response = await request.send();
    var responseData = await response.stream.bytesToString();

    if (response.statusCode == 200) {
      // Parse response data if needed
      return;
    } else {
      throw Exception('Failed to update product images: $responseData');
    }
  }

  static Future<void> deleteProductImage(String productId, String fileName) async {
    final response = await _client.post(
      '$baseUrl/product/delete-image',
      {
        'product_id': productId,
        'file_name': fileName,
      },
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  static Future<void> deleteProduct(String productId) async {
    final response = await _client.post(
      '$baseUrl/product/delete',
      {'product_id': productId},
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  static Future<void> setThumbnailImage(String productId, String fileName) async {
    final response = await _client.post(
      '$baseUrl/set-thumbnail-image',
      {
        'product_id': productId,
        'file_name': fileName,
      },
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  // Wardrobe APIs
  static Future<Map<String, List<dynamic>>> getMyWardrobe() async {
    final response = await _client.post(
      '$baseUrl/get-my-wardrobe',
      {},
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return Map<String, List<dynamic>>.from(data);
  }

  // Cart APIs
  static Future<void> addToCart(String productId, {String? category}) async {
    print('Adding to cart - Product ID: $productId, Category: $category');

    final response = await _client.post(
      '$baseUrl/cart/store',
      {
        'product_id': productId,
        if (category != null) 'category': category,
      },
      headers: await _getAuthHeaders(),
    );

    print('Add to cart response - Status: ${response.statusCode}, Body: ${response.body}');
    _handleResponse(response);
    print('Add to cart successful');
  }

  static Future<List<Map<String, dynamic>>> getCart() async {
    final response = await _client.post(
      '$baseUrl/cart/get',
      {},
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data);
  }

  static Future<void> removeFromCart(String cartItemId) async {
    final response = await _client.delete(
      '$baseUrl/cart/$cartItemId',
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  // Profile APIs
  static Future<void> updateProfile(Map<String, dynamic> profileData) async {
    final response = await _client.post(
      '$baseUrl/update-profile',
      profileData,
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  // Conversation APIs
  static Future<List<Map<String, dynamic>>> getConversations() async {
    final response = await _client.get(
      '$baseUrl/conversations',
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data);
  }

  static Future<String> getConversationId(String userId) async {
    final response = await _client.get(
      '$baseUrl/conversations/get-conversation-id',
      query: {'user_id': userId},
      headers: await _getAuthHeaders(),
    );

    return _handleResponse(response);
  }

  static Future<int> getUnseenMessagesCount() async {
    final response = await _client.get(
      '$baseUrl/conversations/get-unseen-message-count',
      headers: await _getAuthHeaders(),
    );

    return _handleResponse(response);
  }

  static Future<List<Map<String, dynamic>>> getMessages(String conversationId) async {
    final response = await _client.get(
      '$baseUrl/conversations/$conversationId/messages',
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data);
  }

  static Future<void> sendMessage(String conversationId, String content) async {
    final response = await _client.post(
      '$baseUrl/message',
      {
        'conversation_id': conversationId,
        'content': content,
      },
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  static Future<void> markConversationAsSeen(String conversationId) async {
    final response = await _client.post(
      '$baseUrl/conversations/mark-as-seen',
      {'conversation_id': conversationId},
      headers: await _getAuthHeaders(),
    );

    _handleResponse(response);
  }

  // Order APIs
  static Future<List<Order>> getOrders() async {
    final response = await _client.get(
      '$baseUrl/get-orders',
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data)
        .map((json) => Order.fromJson(json))
        .toList();
  }

  // Referral APIs
  static Future<List<Referral>> getReferredUsers() async {
    final response = await _client.get(
      '$baseUrl/get-referred-users',
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data)
        .map((json) => Referral.fromJson(json))
        .toList();
  }

  // City APIs
  static Future<List<Map<String, dynamic>>> getAllCities() async {
    final response = await _client.get(
      '$baseUrl/get-all-city',
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);

    // Handle the case where the API returns a list of strings
    if (data is List) {
      return data.map((city) {
        if (city is String) {
          // Convert each string to a map with 'name' key
          return {'name': city};
        } else if (city is Map<String, dynamic>) {
          // If it's already a map, return it as is
          return city;
        } else {
          // Fallback for unexpected data types
          return {'name': city.toString()};
        }
      }).toList().cast<Map<String, dynamic>>();
    }

    // Fallback to original implementation if data is not a list
    return List<Map<String, dynamic>>.from(data);
  }

  // Color APIs
  static Future<List<Map<String, dynamic>>> getAllColors() async {
    final response = await _client.get(
      '$baseUrl/get-all-colors',
      headers: await _getAuthHeaders(),
    );

    final data = _handleResponse(response);
    return List<Map<String, dynamic>>.from(data);
  }
}
