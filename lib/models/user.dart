class User {
  final String id;
  final String name;
  final String? email;
  final String? googleId;
  final String? facebookId;
  final String? twitterId;
  final String? firebaseToken;
  final String? token;
  final String? profileImage;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  User({
    required this.id,
    required this.name,
    this.email,
    this.googleId,
    this.facebookId,
    this.twitterId,
    this.firebaseToken,
    this.token,
    this.profileImage,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      googleId: json['google_id'],
      facebookId: json['facebook_id'],
      twitterId: json['twitter_id'],
      firebaseToken: json['firebase_token'],
      token: json['token'],
      profileImage: json['profile_image'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'google_id': googleId,
      'facebook_id': facebookId,
      'twitter_id': twitterId,
      'firebase_token': firebaseToken,
      'token': token,
      'profile_image': profileImage,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
