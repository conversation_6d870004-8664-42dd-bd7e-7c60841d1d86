import 'package:Laradrobe/models/product.dart';

class Order {
  final String id;
  final String userId;
  final Product product;
  final String purpose;
  final String from;
  final String to;
  final bool isClean;
  final bool hasCover;
  final bool hasHanger;
  final int securityDepositCost;
  final List<String>? accessories;
  final int rentalCost;
  final int? discountCost;
  final String? status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.userId,
    required this.product,
    required this.purpose,
    required this.from,
    required this.to,
    required this.isClean,
    required this.hasCover,
    required this.hasHanger,
    required this.securityDepositCost,
    this.accessories,
    required this.rentalCost,
    this.discountCost,
    this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      userId: json['user_id'],
      product: Product.fromJson(json['product']),
      purpose: json['purpose'],
      from: json['from'],
      to: json['to'],
      isClean: json['is_clean'] == 1,
      hasCover: json['has_cover'] == 1,
      hasHanger: json['has_hanger'] == 1,
      securityDepositCost: json['security_deposit_cost'],
      accessories: json['accessories'] != null
          ? List<String>.from(json['accessories'])
          : null,
      rentalCost: json['rental_cost'],
      discountCost: json['discount_cost'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'product': product.toJson(),
      'purpose': purpose,
      'from': from,
      'to': to,
      'is_clean': isClean ? 1 : 0,
      'has_cover': hasCover ? 1 : 0,
      'has_hanger': hasHanger ? 1 : 0,
      'security_deposit_cost': securityDepositCost,
      'accessories': accessories,
      'rental_cost': rentalCost,
      'discount_cost': discountCost,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
