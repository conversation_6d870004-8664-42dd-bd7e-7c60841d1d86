class Referral {
  final String id;
  final String referrerId;
  final String referredId;
  final String status;
  final int? reward;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User? referredUser;

  Referral({
    required this.id,
    required this.referrerId,
    required this.referredId,
    required this.status,
    this.reward,
    required this.createdAt,
    required this.updatedAt,
    this.referredUser,
  });

  factory Referral.fromJson(Map<String, dynamic> json) {
    return Referral(
      id: json['id'],
      referrerId: json['referrer_id'],
      referredId: json['referred_id'],
      status: json['status'],
      reward: json['reward'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      referredUser: json['referred_user'] != null
          ? User.fromJson(json['referred_user'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'referrer_id': referrerId,
      'referred_id': referredId,
      'status': status,
      'reward': reward,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'referred_user': referredUser?.toJson(),
    };
  }
}

class User {
  final String id;
  final String name;
  final String? email;
  final String? profileImage;

  User({
    required this.id,
    required this.name,
    this.email,
    this.profileImage,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      profileImage: json['profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profile_image': profileImage,
    };
  }
}
