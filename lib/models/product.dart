class Product {
  final String id;
  final String userId;
  final String category;
  final String name;
  final int purchaseCost;
  final int rentalCost;
  final int securityDepositCost;
  final int rentalDays;
  final String? brandName;
  final String thumbnail;
  final List<ProductImage>? images;
  final User? user;
  final bool isInCart;
  final DateTime? deletedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  Product({
    required this.id,
    required this.userId,
    required this.category,
    required this.name,
    required this.purchaseCost,
    required this.rentalCost,
    required this.securityDepositCost,
    required this.rentalDays,
    this.brandName,
    required this.thumbnail,
    this.images,
    this.user,
    this.isInCart = false,
    this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      userId: json['user_id'],
      category: json['category'],
      name: json['name'],
      purchaseCost: json['purchase_cost'],
      rentalCost: json['rental_cost'],
      securityDepositCost: json['security_deposit_cost'],
      rentalDays: json['rental_days'],
      brandName: json['brand_name'],
      thumbnail: json['thumbnail'],
      images: json['product_image'] != null
          ? List<ProductImage>.from(
              json['product_image'].map((x) => ProductImage.fromJson(x)))
          : null,
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      isInCart: json['is_in_cart'] != null && json['is_in_cart'] > 0,
      deletedAt: json['deleted_at'] != null ? DateTime.parse(json['deleted_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'category': category,
      'name': name,
      'purchase_cost': purchaseCost,
      'rental_cost': rentalCost,
      'security_deposit_cost': securityDepositCost,
      'rental_days': rentalDays,
      'brand_name': brandName,
      'thumbnail': thumbnail,
      'product_image': images?.map((x) => x.toJson()).toList(),
      'user': user?.toJson(),
      'is_in_cart': isInCart ? 1 : 0,
      'deleted_at': deletedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class ProductImage {
  final String id;
  final String productId;
  final String userId;
  final String imageName;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductImage({
    required this.id,
    required this.productId,
    required this.userId,
    required this.imageName,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductImage.fromJson(Map<String, dynamic> json) {
    return ProductImage(
      id: json['id'],
      productId: json['product_id'],
      userId: json['user_id'],
      imageName: json['image_name'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'user_id': userId,
      'image_name': imageName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class User {
  final String id;
  final String name;
  final Profile? profile;

  User({
    required this.id,
    required this.name,
    this.profile,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      profile: json['profile'] != null ? Profile.fromJson(json['profile']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'profile': profile?.toJson(),
    };
  }
}

class Profile {
  final String id;
  final String userId;
  final String? city;
  final String? phone;
  final String? address;
  final String? profileImage;

  Profile({
    required this.id,
    required this.userId,
    this.city,
    this.phone,
    this.address,
    this.profileImage,
  });

  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      id: json['id'],
      userId: json['user_id'],
      city: json['city'],
      phone: json['phone'],
      address: json['address'],
      profileImage: json['profile_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'city': city,
      'phone': phone,
      'address': address,
      'profile_image': profileImage,
    };
  }
}
