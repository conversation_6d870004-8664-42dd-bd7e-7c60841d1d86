import 'package:Laradrobe/models/user.dart';

class Message {
  final String id;
  final String conversationId;
  final String senderId;
  final String content;
  final DateTime createdAt;
  final bool isCurrentUser;

  Message({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.content,
    required this.createdAt,
    this.isCurrentUser = false,
  });

  factory Message.fromJson(Map<String, dynamic> json, String currentUserId) {
    return Message(
      id: json['id'],
      conversationId: json['conversation_id'],
      senderId: json['user_id'],
      content: json['content'],
      createdAt: DateTime.parse(json['created_at']),
      isCurrentUser: json['user_id'] == currentUserId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversation_id': conversationId,
      'user_id': senderId,
      'content': content,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
