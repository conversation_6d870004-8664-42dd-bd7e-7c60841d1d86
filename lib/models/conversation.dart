import 'package:Laradrobe/models/user.dart';

class Conversation {
  final String id;
  final String title;
  final List<User> participants;
  final String lastMessageContent;
  final DateTime? lastMessageTime;
  final bool isSeen;

  Conversation({
    required this.id,
    required this.title,
    required this.participants,
    required this.lastMessageContent,
    this.lastMessageTime,
    this.isSeen = true,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'],
      title: json['title'] ?? '',
      participants: json['participants'] != null
          ? List<User>.from(json['participants'].map((x) => User.fromJson(x)))
          : [],
      lastMessageContent: json['last_message_content'] ?? '',
      lastMessageTime: json['last_message_time'] != null
          ? DateTime.parse(json['last_message_time'])
          : null,
      isSeen: json['is_seen'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'participants': participants.map((x) => x.toJson()).toList(),
      'last_message_content': lastMessageContent,
      'last_message_time': lastMessageTime?.toIso8601String(),
      'is_seen': isSeen,
    };
  }
}
