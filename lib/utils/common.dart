import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:Laradrobe/theme/app_theme.dart';

/// Format currency in Indian Rupees
String formatCurrency(int amount) {
  final formatter = NumberFormat.currency(
    symbol: '₹',
    locale: 'en_IN',
    decimalDigits: 0,
  );
  return formatter.format(amount);
}

/// Format date in a readable format
String formatDate(DateTime date) {
  final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);
  final yesterday = today.subtract(const Duration(days: 1));
  final dateToCheck = DateTime(date.year, date.month, date.day);
  
  if (dateToCheck == today) {
    return 'Today';
  } else if (dateToCheck == yesterday) {
    return 'Yesterday';
  } else if (now.difference(date).inDays < 7) {
    return DateFormat('EEEE').format(date); // e.g., Monday, Tuesday
  } else {
    return DateFormat('MMM d, yyyy').format(date); // e.g., Jan 1, 2023
  }
}

/// Format time in a readable format
String formatTime(DateTime time) {
  return DateFormat('h:mm a').format(time); // e.g., 2:30 PM
}

/// Show a custom snackbar
void showSnackbar({
  required String title,
  required String message,
  SnackPosition position = SnackPosition.BOTTOM,
  Color? backgroundColor,
  Duration duration = const Duration(seconds: 3),
}) {
  Get.snackbar(
    title,
    message,
    snackPosition: position,
    backgroundColor: backgroundColor ?? AppTheme.primaryColor.withOpacity(0.9),
    colorText: AppTheme.textDark,
    borderRadius: 8,
    margin: const EdgeInsets.all(16),
    duration: duration,
    isDismissible: true,
    dismissDirection: DismissDirection.horizontal,
    forwardAnimationCurve: Curves.easeOutBack,
  );
}

/// Show a success snackbar
void showSuccessSnackbar({
  required String message,
  String title = 'Success',
  SnackPosition position = SnackPosition.BOTTOM,
  Duration duration = const Duration(seconds: 3),
}) {
  showSnackbar(
    title: title,
    message: message,
    position: position,
    backgroundColor: AppTheme.successColor.withOpacity(0.9),
    duration: duration,
  );
}

/// Show an error snackbar
void showErrorSnackbar({
  required String message,
  String title = 'Error',
  SnackPosition position = SnackPosition.BOTTOM,
  Duration duration = const Duration(seconds: 3),
}) {
  Get.snackbar(
    title,
    message,
    snackPosition: position,
    backgroundColor: const Color(0xFFD32F2F), // Darker red for better contrast
    colorText: Colors.white,
    borderRadius: 12,
    margin: const EdgeInsets.all(16),
    duration: duration,
    isDismissible: true,
    dismissDirection: DismissDirection.horizontal,
    forwardAnimationCurve: Curves.easeOutBack,
    icon: const Icon(
      Icons.error_outline,
      color: Colors.white,
    ),
  );
}

/// Show a confirmation dialog
Future<bool> showConfirmationDialog({
  required String title,
  required String message,
  String confirmText = 'Confirm',
  String cancelText = 'Cancel',
  Color? confirmColor,
  Color? cancelColor,
}) async {
  final result = await Get.dialog<bool>(
    AlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Get.back(result: false),
          style: TextButton.styleFrom(
            foregroundColor: cancelColor ?? AppTheme.textMedium,
          ),
          child: Text(cancelText),
        ),
        TextButton(
          onPressed: () => Get.back(result: true),
          style: TextButton.styleFrom(
            foregroundColor: confirmColor ?? AppTheme.accentColor,
          ),
          child: Text(confirmText),
        ),
      ],
    ),
  );
  
  return result ?? false;
}

/// Show a loading dialog
void showLoadingDialog({String message = 'Loading...'}) {
  Get.dialog(
    Dialog(
      backgroundColor: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    ),
    barrierDismissible: false,
  );
}

/// Hide the loading dialog
void hideLoadingDialog() {
  if (Get.isDialogOpen ?? false) {
    Get.back();
  }
}

/// Validate email address
bool isValidEmail(String email) {
  final emailRegExp = RegExp(r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+');
  return emailRegExp.hasMatch(email);
}

/// Validate phone number
bool isValidPhone(String phone) {
  final phoneRegExp = RegExp(r'^\d{10}$');
  return phoneRegExp.hasMatch(phone);
}

/// Get initials from name
String getInitials(String name) {
  if (name.isEmpty) return '';
  
  final nameParts = name.split(' ');
  if (nameParts.length == 1) {
    return nameParts[0][0].toUpperCase();
  }
  
  return nameParts[0][0].toUpperCase() + nameParts[1][0].toUpperCase();
}

/// Calculate discount percentage
int calculateDiscountPercentage(int originalPrice, int discountedPrice) {
  if (originalPrice <= 0) return 0;
  return ((originalPrice - discountedPrice) / originalPrice * 100).round();
}

/// Truncate text with ellipsis
String truncateText(String text, int maxLength) {
  if (text.length <= maxLength) return text;
  return '${text.substring(0, maxLength)}...';
}

/// Get appropriate text color based on background color
Color getTextColorForBackground(Color backgroundColor) {
  // Calculate the luminance of the background color
  final luminance = backgroundColor.computeLuminance();
  
  // Use white text for dark backgrounds, black text for light backgrounds
  return luminance > 0.5 ? Colors.black : Colors.white;
}
