import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/utils/storage_helper.dart';

class AuthProvider extends GetxController {
  final RxMap<String, dynamic> user = <String, dynamic>{}.obs;
  final RxBool isLoading = false.obs;
  final RxBool isAuthenticated = false.obs;

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    serverClientId: '128217929900-02asfu3tfr1rdf28trn2kqlg9hva48pa.apps.googleusercontent.com',
  );

  @override
  void onInit() {
    super.onInit();
    checkAuthStatus();
  }

  Future<void> checkAuthStatus() async {
    isLoading.value = true;

    try {
      final token = await StorageHelper.getToken();

      if (token != null) {
        // Verify token with the server
        final userData = await ApiService.verifyToken();

        if (userData != null) {
          user.value = userData;
          isAuthenticated.value = true;
        } else {
          // Token is invalid, clear storage
          await _clearUserData();
        }
      }
    } catch (e) {
      print('Error checking auth status: $e');
      await _clearUserData();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> signInWithGoogle() async {
    isLoading.value = true;

    try {
      // Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        isLoading.value = false;
        return;
      }

      // Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Get Firebase token
      final String? firebaseToken = await FirebaseMessaging.instance.getToken();

      // Sign in with the backend
      final userData = await ApiService.googleSignIn(
        googleAuth.accessToken!,
        firebaseToken ?? '',
      );

      // Save user data and token
      await _saveUserData(userData);

      // Update state
      user.value = userData;
      isAuthenticated.value = true;

      // Navigate to home after the current frame is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAllNamed(Routes.main);
      });
    } catch (e) {
      print('Error signing in with Google: $e');
      Get.snackbar(
        'Error',
        'Failed to sign in with Google. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> signInWithFacebook() async {
    isLoading.value = true;

    try {
      // Facebook sign-in implementation
      // This would use flutter_facebook_auth package

      Get.snackbar(
        'Not Implemented',
        'Facebook sign-in is not implemented in this version.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error signing in with Facebook: $e');
      Get.snackbar(
        'Error',
        'Failed to sign in with Facebook. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> signInWithTwitter() async {
    isLoading.value = true;

    try {
      // Twitter sign-in implementation
      // This would require a Twitter SDK integration

      Get.snackbar(
        'Not Implemented',
        'Twitter sign-in is not implemented in this version.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error signing in with Twitter: $e');
      Get.snackbar(
        'Error',
        'Failed to sign in with Twitter. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> logout() async {
    isLoading.value = true;

    try {
      // Sign out from the backend
      await ApiService.logout();

      // Sign out from Google
      await _googleSignIn.signOut();

      // Clear user data
      await _clearUserData();

      // Update state
      user.clear();
      isAuthenticated.value = false;

      // Navigate to login after the current frame is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAllNamed(Routes.login);
      });
    } catch (e) {
      print('Error logging out: $e');
      Get.snackbar(
        'Error',
        'Failed to log out. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _saveUserData(Map<String, dynamic> userData) async {
    // Save token
    if (userData['token'] != null) {
      await StorageHelper.saveToken(userData['token']);
    }

    // Save user data
    await StorageHelper.saveUser(jsonEncode(userData));
  }

  Future<void> _clearUserData() async {
    await StorageHelper.removeToken();
    await StorageHelper.removeUser();
  }
}
