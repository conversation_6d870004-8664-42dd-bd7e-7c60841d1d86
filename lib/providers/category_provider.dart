import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';

class CategoryProvider extends GetxController {
  final RxList<Map<String, dynamic>> categories = <Map<String, dynamic>>[].obs;
  final RxString selectedCategory = ''.obs;
  final RxBool isLoading = false.obs;
  bool _hasAttemptedFetch = false; // Flag to track if fetch has been attempted
  
  // Predefined categories with images (fallback if API fails)
  // final List<Map<String, dynamic>> predefinedCategories = [
  //   {"name": "Sherwani", "image": "sherwani.jpg"},
  //   {"name": "Lehenga", "image": "lehenga.jpg"},
  //   {"name": "Blazer", "image": "blazer.jpg"},
  //   {"name": "Coat", "image": "coat.jpg"},
  //   {"name": "Saree", "image": "saree.jpg"},
  // ];
  
  @override
  void onInit() {
    super.onInit();
    print('CategoryProvider onInit called');
    fetchCategories();
  }
  
  Future<void> fetchCategories() async {
    print('fetchCategories called - _hasAttemptedFetch: $_hasAttemptedFetch, categories.length: ${categories.length}');

    if (_hasAttemptedFetch && categories.isNotEmpty) {
      // Only skip if we already have categories
      print('Categories already loaded, skipping fetch');
      return;
    }

    print('Starting to fetch categories...');
    isLoading.value = true;
    _hasAttemptedFetch = true; // Set the flag indicating a fetch attempt is being made

    try {
      final response = await ApiService.getAllCategories();

      print('Raw API response: $response');
      print('Response type: ${response.runtimeType}');
      print('Response length: ${response.length}');

      if (response.isNotEmpty) {
        categories.value = response;
        print('Categories loaded from API: ${categories.length} categories');
        print('First category: ${categories.isNotEmpty ? categories[0] : 'None'}');
      } else {
        // API returned empty, keep categories empty
        categories.value = [];
        print('API returned empty categories list');
      }
    } catch (e) {
      print('Error fetching categories: $e');
      print('Error type: ${e.runtimeType}');
      // Keep categories empty if API fails
      categories.value = [];

      // Show user-friendly error
      Get.snackbar(
        'Error',
        'Failed to load categories. Please check your internet connection.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void setSelectedCategory(String category) {
    selectedCategory.value = category;
  }

  void clearSelectedCategory() {
    selectedCategory.value = '';
  }

  // Method to retry fetching categories
  Future<void> retryFetchCategories() async {
    _hasAttemptedFetch = false; // Reset the flag to allow retry
    await fetchCategories();
  }

  // Method to force fetch categories (for testing)
  Future<void> forceFetchCategories() async {
    print('Force fetching categories...');
    _hasAttemptedFetch = false;
    categories.clear(); // Clear existing categories
    await fetchCategories();
  }

  // Method to fetch categories without any guards (for debugging)
  Future<void> debugFetchCategories() async {
    print('DEBUG: Starting fresh category fetch...');
    isLoading.value = true;

    try {
      print('DEBUG: Calling ApiService.getAllCategories()');
      final response = await ApiService.getAllCategories();

      print('DEBUG: Raw API response: $response');
      print('DEBUG: Response type: ${response.runtimeType}');
      print('DEBUG: Response length: ${response.length}');

      if (response.isNotEmpty) {
        categories.value = response;
        print('DEBUG: Categories set successfully: ${categories.length} categories');
        print('DEBUG: First category: ${categories.isNotEmpty ? categories[0] : 'None'}');
      } else {
        categories.value = [];
        print('DEBUG: API returned empty categories list');
      }
    } catch (e) {
      print('DEBUG: Error fetching categories: $e');
      print('DEBUG: Error type: ${e.runtimeType}');
      categories.value = [];
    } finally {
      isLoading.value = false;
      print('DEBUG: Fetch completed. Final categories count: ${categories.length}');
    }
  }
}
