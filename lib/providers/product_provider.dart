import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';

class ProductProvider extends GetxController {
  final RxMap<String, dynamic> product = <String, dynamic>{}.obs;
  final RxList<String> productImages = <String>[].obs;
  final RxList<String> productOriginalImages = <String>[].obs;
  final RxString productId = ''.obs;
  final RxBool isLoading = false.obs;
  final RxBool isUploading = false.obs;
  final RxBool isUpdating = false.obs;
  final RxBool isDeleting = false.obs;
  final RxInt currentImageIndex = 0.obs;

  Future<void> fetchProductById(String id) async {
    if (id.isEmpty) return;

    isLoading.value = true;
    productId.value = id;

    try {
      final response = await ApiService.getProductById(id);
      product.value = response;

      // Extract images
      if (response['product_image'] != null) {
        final images = List<Map<String, dynamic>>.from(response['product_image']);
        productImages.value = images.map((img) => '${ApiService.productImageUrl}${img['image_name']}').toList();
        productOriginalImages.value = images.map((img) => '${ApiService.productImageUrl}${img['image_name']}').toList();
      }
    } catch (e) {
      print('Error fetching product: $e');
      Get.snackbar(
        'Error',
        'Failed to load product details. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> uploadProduct(Map<String, dynamic> productData, List<dynamic> files) async {
    isUploading.value = true;

    try {
      await ApiService.uploadProduct(productData, files);
      Get.snackbar(
        'Success',
        'Product uploaded successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      print('Error uploading product: $e');
      Get.snackbar(
        'Error',
        'Failed to upload product. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isUploading.value = false;
    }
  }

  Future<bool> updateProduct(Map<String, dynamic> productData) async {
    isUpdating.value = true;

    try {
      await ApiService.updateProduct(productData);
      Get.snackbar(
        'Success',
        'Product updated successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      print('Error updating product: $e');
      Get.snackbar(
        'Error',
        'Failed to update product. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isUpdating.value = false;
    }
  }

  Future<bool> deleteProduct() async {
    if (productId.value.isEmpty) return false;

    isDeleting.value = true;

    try {
      await ApiService.deleteProduct(productId.value);
      Get.snackbar(
        'Success',
        'Product deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      print('Error deleting product: $e');
      Get.snackbar(
        'Error',
        'Failed to delete product. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isDeleting.value = false;
    }
  }

  Future<bool> updateProductImages(List<dynamic> files) async {
    if (productId.value.isEmpty) return false;

    isUpdating.value = true;

    try {
      await ApiService.updateProductImages(productId.value, files);
      Get.snackbar(
        'Success',
        'Product images updated successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      print('Error updating product images: $e');
      Get.snackbar(
        'Error',
        'Failed to update product images. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isUpdating.value = false;
    }
  }

  Future<bool> deleteProductImage(String fileName) async {
    if (productId.value.isEmpty) return false;

    isUpdating.value = true;

    try {
      await ApiService.deleteProductImage(productId.value, fileName);

      // Remove the image from the productImages list
      final imageUrl = productImages.firstWhere(
        (url) => url.contains(fileName),
        orElse: () => '',
      );

      if (imageUrl.isNotEmpty) {
        productImages.remove(imageUrl);
      }

      Get.snackbar(
        'Success',
        'Image deleted successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      print('Error deleting product image: $e');
      Get.snackbar(
        'Error',
        'Failed to delete image. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isUpdating.value = false;
    }
  }

  void setCurrentImageIndex(int index) {
    if (index >= 0 && index < productImages.length) {
      currentImageIndex.value = index;
    }
  }

  void clearProduct() {
    product.clear();
    productImages.clear();
    productOriginalImages.clear();
    productId.value = '';
    currentImageIndex.value = 0;
  }
}
