import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/utils/storage_helper.dart';

class ThemeProvider extends GetxController {
  final Rx<ThemeMode> themeMode = ThemeMode.system.obs;
  
  @override
  void onInit() {
    super.onInit();
    _loadTheme();
  }
  
  Future<void> _loadTheme() async {
    final savedTheme = await StorageHelper.getTheme();
    
    if (savedTheme != null) {
      switch (savedTheme) {
        case 'light':
          themeMode.value = ThemeMode.light;
          break;
        case 'dark':
          themeMode.value = ThemeMode.dark;
          break;
        default:
          themeMode.value = ThemeMode.system;
      }
    }
  }
  
  Future<void> setThemeMode(ThemeMode mode) async {
    themeMode.value = mode;
    
    String themeString;
    switch (mode) {
      case ThemeMode.light:
        themeString = 'light';
        break;
      case ThemeMode.dark:
        themeString = 'dark';
        break;
      default:
        themeString = 'system';
    }
    
    await StorageHelper.saveTheme(themeString);
  }
  
  bool get isDarkMode {
    if (themeMode.value == ThemeMode.system) {
      return Get.isPlatformDarkMode;
    }
    return themeMode.value == ThemeMode.dark;
  }
  
  void toggleTheme() {
    if (isDarkMode) {
      setThemeMode(ThemeMode.light);
    } else {
      setThemeMode(ThemeMode.dark);
    }
  }
}
