import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/providers/cart_provider.dart';

class HomeProvider extends GetxController {
  // Loading states
  final RxBool isInitialLoading = false.obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool isLastPage = false.obs;

  // Products
  final RxList<Map<String, dynamic>> products = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> newArrivals = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> bestProducts = <Map<String, dynamic>>[].obs;

  // Pagination
  final RxInt page = 1.obs;

  // Fetch products
  Future<void> fetchProducts(String city, {String? category}) async {
    if (isLoading.value) return;

    isInitialLoading.value = true;
    isLoading.value = true;

    try {
      final response = await ApiService.fetchProductsByCityCategory(
        city,
        category,
        page.value,
      );

      // Filter products by feature with null safety
      newArrivals.value = response
          .where((product) => product['feature'] == 'new')
          .map((product) => _sanitizeProduct(product))
          .toList();

      bestProducts.value = response
          .where((product) => product['feature'] == 'best')
          .map((product) => _sanitizeProduct(product))
          .toList();

      products.value = response
          .where((product) => product['feature'] == null)
          .map((product) => _sanitizeProduct(product))
          .toList();

      // Check if this is the last page
      isLastPage.value = response.isEmpty;
    } catch (e) {
      print('Error fetching products: $e');
    } finally {
      isInitialLoading.value = false;
      isLoading.value = false;
    }
  }

  // Sanitize product data to ensure all required fields exist
  Map<String, dynamic> _sanitizeProduct(Map<String, dynamic> product) {
    // Ensure all required fields exist with default values if missing
    return {
      'id': product['id'] ?? '',
      'name': product['name'] ?? 'Unknown Product',
      'category': product['category'] ?? 'Uncategorized',
      'thumbnail': product['thumbnail'] ?? '',
      'rental_cost': product['rental_cost'] ?? 0,
      'purchase_cost': product['purchase_cost'] ?? 0,
      'is_in_cart': product['is_in_cart'] ?? 0,
      'feature': product['feature'],
      // Copy all other fields
      ...product,
    };
  }

  // Load more products (pagination)
  Future<void> loadMoreProducts(String city, {String? category}) async {
    if (isLoading.value || isLoadingMore.value || isLastPage.value) return;

    isLoadingMore.value = true;

    try {
      page.value++;

      final response = await ApiService.fetchProductsByCityCategory(
        city,
        category,
        page.value,
      );

      // Filter and add new products with sanitization
      final newProducts = response
          .where((product) => product['feature'] == null)
          .map((product) => _sanitizeProduct(product))
          .toList();
      products.addAll(newProducts);

      // Update new arrivals and best products if needed
      final newNewArrivals = response
          .where((product) => product['feature'] == 'new')
          .map((product) => _sanitizeProduct(product))
          .toList();
      if (newNewArrivals.isNotEmpty) {
        newArrivals.addAll(newNewArrivals);
      }

      final newBestProducts = response
          .where((product) => product['feature'] == 'best')
          .map((product) => _sanitizeProduct(product))
          .toList();
      if (newBestProducts.isNotEmpty) {
        bestProducts.addAll(newBestProducts);
      }

      // Check if this is the last page
      isLastPage.value = response.isEmpty;
    } catch (e) {
      print('Error loading more products: $e');
    } finally {
      isLoadingMore.value = false;
    }
  }

  // Reset and fetch products
  Future<void> resetAndFetchProducts(String city, {String? category}) async {
    // Reset pagination
    page.value = 1;
    isLastPage.value = false;

    // Clear products
    products.clear();
    newArrivals.clear();
    bestProducts.clear();

    // Fetch products
    await fetchProducts(city, category: category);
  }

  // Add product to cart
  Future<bool> toggleFavorite(String productId, {String? category}) async {
    try {
      // Find product in lists
      final productIndex = products.indexWhere((p) => p['id'] == productId);
      final newArrivalIndex = newArrivals.indexWhere((p) => p['id'] == productId);
      final bestProductIndex = bestProducts.indexWhere((p) => p['id'] == productId);

      // Toggle is_in_cart value
      if (productIndex != -1) {
        final isInCart = products[productIndex]['is_in_cart'] ?? 0;

        if (isInCart > 0) {
          // Remove from cart
          await ApiService.removeFromCart(productId);
          products[productIndex]['is_in_cart'] = 0;
        } else {
          // Add to cart
          await ApiService.addToCart(productId, category: category);
          products[productIndex]['is_in_cart'] = 1;
        }

        products.refresh();
      }

      // Update in new arrivals list if exists
      if (newArrivalIndex != -1) {
        final isInCart = newArrivals[newArrivalIndex]['is_in_cart'] ?? 0;
        newArrivals[newArrivalIndex]['is_in_cart'] = isInCart > 0 ? 0 : 1;
        newArrivals.refresh();
      }

      // Update in best products list if exists
      if (bestProductIndex != -1) {
        final isInCart = bestProducts[bestProductIndex]['is_in_cart'] ?? 0;
        bestProducts[bestProductIndex]['is_in_cart'] = isInCart > 0 ? 0 : 1;
        bestProducts.refresh();
      }

      // Refresh CartProvider to update favorites screen
      try {
        final cartProvider = Get.find<CartProvider>();
        await cartProvider.fetchCart();
        print('CartProvider refreshed after favorite toggle');
      } catch (e) {
        print('CartProvider not found or failed to refresh: $e');
      }

      return true;
    } catch (e) {
      print('Error toggling favorite: $e');
      // Return false if there was an error (like unauthorized)
      return false;
    }
  }
}
