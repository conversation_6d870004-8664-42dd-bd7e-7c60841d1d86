import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/utils/storage_helper.dart';

class LanguageProvider extends GetxController {
  final Rx<Locale> locale = const Locale('en', 'US').obs;
  final RxString language = 'English'.obs;
  
  final List<Map<String, dynamic>> supportedLanguages = [
    {'name': 'English', 'locale': const Locale('en', 'US')},
    {'name': 'हिन्दी', 'locale': const Locale('hi', 'IN')},
    {'name': 'Hinglish', 'locale': const Locale('he', 'IN-US')},
  ];
  
  @override
  void onInit() {
    super.onInit();
    _loadLanguage();
  }
  
  Future<void> _loadLanguage() async {
    final savedLanguage = await StorageHelper.getLanguage();
    
    if (savedLanguage != null) {
      language.value = savedLanguage;
      _updateLocale();
    }
  }
  
  void _updateLocale() {
    for (final lang in supportedLanguages) {
      if (lang['name'] == language.value) {
        locale.value = lang['locale'];
        Get.updateLocale(locale.value);
        break;
      }
    }
  }
  
  Future<void> setLanguage(String languageName) async {
    language.value = languageName;
    await StorageHelper.saveLanguage(languageName);
    _updateLocale();
  }
}
