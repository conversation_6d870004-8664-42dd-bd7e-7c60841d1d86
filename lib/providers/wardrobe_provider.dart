import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/providers/auth_provider.dart'; // Added import

class WardrobeProvider extends GetxController {
  final RxMap<String, List<dynamic>> wardrobe = <String, List<dynamic>>{}.obs;
  final RxBool isLoading = false.obs;
  final RxBool isEmpty = true.obs;
  late final AuthProvider _authProvider; // Added AuthProvider instance

  @override
  void onInit() {
    super.onInit();
    _authProvider = Get.find<AuthProvider>(); // Initialize AuthProvider

    // Listen to authentication status
    ever(_authProvider.isAuthenticated, (bool isAuthenticated) {
      if (isAuthenticated) {
        fetchWardrobe();
      } else {
        // Optionally clear wardrobe data if user logs out
        wardrobe.clear();
        isEmpty.value = true;
      }
    });

    // Initial fetch if already authenticated (e.g., app restart with valid token)
    if (_authProvider.isAuthenticated.value) {
      fetchWardrobe();
    }
  }

  Future<void> fetchWardrobe() async {
    isLoading.value = true;
    
    try {
      final response = await ApiService.getMyWardrobe();
      wardrobe.value = response;
      isEmpty.value = response.isEmpty;
    } catch (e) {
      print('Error fetching wardrobe: $e');
      Get.snackbar(
        'Error',
        'Failed to load your wardrobe. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  void removeProductFromWardrobe(String category, String productId) {
    if (wardrobe.containsKey(category)) {
      final products = wardrobe[category]!;
      final index = products.indexWhere((product) => product['id'] == productId);
      
      if (index != -1) {
        products.removeAt(index);
        
        // If category is empty, remove it
        if (products.isEmpty) {
          wardrobe.remove(category);
        } else {
          wardrobe[category] = products;
        }
        
        // Check if wardrobe is empty
        isEmpty.value = wardrobe.isEmpty;
      }
    }
  }
  
  void addProductToWardrobe(String category, Map<String, dynamic> product) {
    if (wardrobe.containsKey(category)) {
      final products = wardrobe[category]!;
      products.add(product);
      wardrobe[category] = products;
    } else {
      wardrobe[category] = [product];
    }
    
    isEmpty.value = false;
  }
}
