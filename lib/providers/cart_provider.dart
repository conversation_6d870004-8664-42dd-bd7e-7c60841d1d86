import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/providers/auth_provider.dart'; // Added import

class CartProvider extends GetxController {
  final RxList<Map<String, dynamic>> cart = <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isEmpty = true.obs;
  late final AuthProvider _authProvider; // Added AuthProvider instance
  
  @override
  void onInit() {
    super.onInit();
    _authProvider = Get.find<AuthProvider>(); // Initialize AuthProvider

    // Listen to authentication status
    ever(_authProvider.isAuthenticated, (bool isAuthenticated) {
      if (isAuthenticated) {
        fetchCart();
      } else {
        // Clear cart data if user logs out
        cart.clear();
        isEmpty.value = true;
      }
    });

    // Initial fetch if already authenticated
    if (_authProvider.isAuthenticated.value) {
      fetchCart();
    }
  }
  
  Future<void> fetchCart() async {
    isLoading.value = true;
    
    try {
      final response = await ApiService.getCart();
      cart.value = response;
      isEmpty.value = response.isEmpty;
    } catch (e) {
      print('Error fetching cart: $e');
      Get.snackbar(
        'Error',
        'Failed to load your favorites. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> addToCart(String productId, {String? category}) async {
    try {
      await ApiService.addToCart(productId, category: category);
      await fetchCart();
      
      Get.snackbar(
        'Success',
        'Added to favorites',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error adding to cart: $e');
      Get.snackbar(
        'Error',
        'Failed to add to favorites. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
  
  Future<void> removeFromCart(String cartItemId) async {
    try {
      await ApiService.removeFromCart(cartItemId);
      
      // Remove from local list
      cart.removeWhere((item) => item['id'] == cartItemId);
      isEmpty.value = cart.isEmpty;
      
      Get.snackbar(
        'Success',
        'Removed from favorites',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      print('Error removing from cart: $e');
      Get.snackbar(
        'Error',
        'Failed to remove from favorites. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
