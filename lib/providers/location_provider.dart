import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/utils/storage_helper.dart';

class LocationProvider extends GetxController {
  final RxString city = ''.obs;
  final RxList<Map<String, dynamic>> cities = <Map<String, dynamic>>[].obs;
  final RxBool isLoadingCities = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    _loadSavedCity();
    fetchCities();
  }
  
  Future<void> _loadSavedCity() async {
    final savedCity = await StorageHelper.getCity();
    if (savedCity != null && savedCity.isNotEmpty) {
      city.value = savedCity;
    }
  }
  
  Future<void> fetchCities() async {
    isLoadingCities.value = true;

    try {
      final response = await ApiService.getAllCities();
      cities.value = response;
      print('Cities fetched from backend: ${cities.length} cities'); // Debug log
      if (cities.isNotEmpty) {
        print('First city: ${cities[0]}'); // Debug log
      }
    } catch (e) {
      print('Error fetching cities: $e');
    } finally {
      isLoadingCities.value = false;
    }
  }
  
  Future<void> setCity(String cityName) async {
    city.value = cityName;
    await StorageHelper.saveCity(cityName);
  }
}
