import 'package:get/get.dart';
import 'package:Laradrobe/api/api_service.dart';
import 'package:Laradrobe/models/conversation.dart';
import 'package:Laradrobe/models/message.dart';

class ConversationProvider extends GetxController {
  final RxList<Map<String, dynamic>> conversations = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> messages = <Map<String, dynamic>>[].obs;
  final RxInt unseenMessagesCount = 0.obs;
  final RxBool isLoadingConversations = false.obs;
  final RxBool isLoadingMessages = false.obs;
  final RxBool isSendingMessage = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    // fetchConversations();
    // getUnseenMessagesCount();
  }
  
  Future<void> fetchConversations() async {
    isLoadingConversations.value = true;
    
    try {
      final response = await ApiService.getConversations();
      conversations.value = response;
    } catch (e) {
      print('Error fetching conversations: $e');
    } finally {
      isLoadingConversations.value = false;
    }
  }
  
  Future<void> getUnseenMessagesCount() async {
    try {
      final count = await ApiService.getUnseenMessagesCount();
      unseenMessagesCount.value = count;
    } catch (e) {
      print('Error getting unseen messages count: $e');
    }
  }
  
  Future<void> fetchMessages(String conversationId) async {
    isLoadingMessages.value = true;
    
    try {
      final response = await ApiService.getMessages(conversationId);
      messages.value = response;
      
      // Mark conversation as seen
      await markConversationAsSeen(conversationId);
    } catch (e) {
      print('Error fetching messages: $e');
    } finally {
      isLoadingMessages.value = false;
    }
  }
  
  Future<void> sendMessage(String conversationId, String content) async {
    if (content.trim().isEmpty) return;
    
    isSendingMessage.value = true;
    
    try {
      await ApiService.sendMessage(conversationId, content);
      
      // Refresh messages
      await fetchMessages(conversationId);
    } catch (e) {
      print('Error sending message: $e');
    } finally {
      isSendingMessage.value = false;
    }
  }
  
  Future<void> markConversationAsSeen(String conversationId) async {
    try {
      await ApiService.markConversationAsSeen(conversationId);
      
      // Update unseen messages count
      await getUnseenMessagesCount();
    } catch (e) {
      print('Error marking conversation as seen: $e');
    }
  }
  
  Future<String> getConversationId(String userId) async {
    try {
      final conversationId = await ApiService.getConversationId(userId);
      return conversationId;
    } catch (e) {
      print('Error getting conversation ID: $e');
      return '';
    }
  }
}
