import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/theme/app_theme.dart';

class NotificationService {
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;

  Future<void> initialize() async {
    // Request permissions for iOS devices
    await _fcm.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // Handle foreground notifications
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background notifications
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

    // Handle initial notification when app is launched from terminated state
    final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _handleInitialMessage(initialMessage);
    }
  }

  void _handleForegroundMessage(RemoteMessage message) {
    if (message.notification != null) {
      // Show a snackbar for foreground notifications
      Get.snackbar(
        message.notification?.title ?? 'New Notification',
        message.notification?.body ?? '',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        backgroundColor: AppTheme.infoColor,
        colorText: Colors.white,
        borderRadius: 12,
        margin: const EdgeInsets.all(16),
        icon: const Icon(
          Icons.notifications,
          color: Colors.white,
        ),
        onTap: (_) {
          _handleNotificationTap(message.data);
        },
      );
    }
  }

  void _handleBackgroundMessage(RemoteMessage message) {
    _handleNotificationTap(message.data);
  }

  void _handleInitialMessage(RemoteMessage message) {
    // Handle initial message when app is launched from terminated state
    Future.delayed(const Duration(seconds: 1), () {
      _handleNotificationTap(message.data);
    });
  }

  void _handleNotificationTap(Map<String, dynamic> data) {
    // Navigate based on notification type
    if (data.containsKey('conversationId')) {
      Get.toNamed('/messages', parameters: {
        "name": data['name'] ?? '',
        "id": data['id'] ?? '',
        "conversationId": data['conversationId'] ?? '',
      });
    } else if (data.containsKey('productId')) {
      Get.toNamed('/product', parameters: {
        "productId": data['productId'] ?? '',
      });
    } else if (data.containsKey('orderId')) {
      Get.toNamed('/orders');
    }
  }

  Future<String?> getFcmToken() async {
    return await _fcm.getToken();
  }
}
