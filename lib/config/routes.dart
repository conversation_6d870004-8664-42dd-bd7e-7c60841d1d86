import 'package:get/get.dart';
import 'package:Laradrobe/screens/auth/login_screen.dart';
import 'package:Laradrobe/screens/cart/cart_screen.dart';
import 'package:Laradrobe/screens/category/category_screen.dart';
import 'package:Laradrobe/screens/chat/conversation_screen.dart';
import 'package:Laradrobe/screens/chat/messages_screen.dart';
import 'package:Laradrobe/screens/home/<USER>';
import 'package:Laradrobe/screens/main/main_screen.dart';
import 'package:Laradrobe/screens/onboarding/onboarding_screen.dart';
import 'package:Laradrobe/screens/orders/order_screen.dart';
import 'package:Laradrobe/screens/product/product_detail_screen.dart';
import 'package:Laradrobe/screens/product/product_update_screen.dart';
import 'package:Laradrobe/screens/product/product_upload_screen.dart';
import 'package:Laradrobe/screens/profile/edit_profile_screen.dart';
import 'package:Laradrobe/screens/profile/profile_screen.dart';
import 'package:Laradrobe/screens/referral/referral_screen.dart';
import 'package:Laradrobe/screens/search/search_screen.dart';
import 'package:Laradrobe/screens/wardrobe/wardrobe_screen.dart';

class Routes {
  // Route names
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String main = '/main';
  static const String home = '/home';
  static const String category = '/category';
  static const String productDetail = '/product';
  static const String productUpload = '/product/upload';
  static const String productUpdate = '/product/update';
  static const String wardrobe = '/wardrobe';
  static const String cart = '/cart';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String conversations = '/conversations';
  static const String messages = '/messages';
  static const String orders = '/orders';
  static const String referral = '/referral';
  static const String search = '/search';

  // Get pages
  static final List<GetPage> pages = [
    GetPage(
      name: onboarding,
      page: () => const OnboardingScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: login,
      page: () => const LoginScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: main,
      page: () => const MainScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: home,
      page: () => const HomeScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: category,
      page: () => const CategoryScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: productDetail,
      page: () => const ProductDetailScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: productUpload,
      page: () => const ProductUploadScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: productUpdate,
      page: () => const ProductUpdateScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: wardrobe,
      page: () => const WardrobeScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: cart,
      page: () => const CartScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: profile,
      page: () => const ProfileScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: editProfile,
      page: () => const EditProfileScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: conversations,
      page: () => const ConversationScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: messages,
      page: () => const MessagesScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: orders,
      page: () => const OrderScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: referral,
      page: () => const ReferralScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: search,
      page: () => const SearchScreen(),
      transition: Transition.rightToLeft,
    ),
  ];
}
