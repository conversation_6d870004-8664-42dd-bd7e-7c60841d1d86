import 'package:flutter/foundation.dart';

class Env {
  // static const String fileName = '.env';

  static String get fileName {
    if (kReleaseMode) {
      return '.env.prod';
    }
    return '.env.dev';
  }
  
  // API endpoints
  static const String apiBaseUrl = 'API_BASE_URL';
  static const String productImageUrl = 'PRODUCT_IMAGE_URL';
  static const String ProductOriginalUrl = 'PRODUCT_ORIGINAL_URL';
  
  // Social auth
  static const String googleClientId = 'GOOGLE_CLIENT_ID';
  static const String facebookAppId = 'FACEBOOK_APP_ID';
  static const String twitterKey = 'TWITTER_KEY';
  static const String twitterSecret = 'TWITTER_SECRET';
}