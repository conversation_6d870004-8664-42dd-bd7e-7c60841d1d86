import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:Laradrobe/config/env.dart';
import 'package:Laradrobe/config/routes.dart';
import 'package:Laradrobe/providers/auth_provider.dart';
import 'package:Laradrobe/providers/bottom_navigation_provider.dart';
import 'package:Laradrobe/providers/cart_provider.dart';
import 'package:Laradrobe/providers/category_provider.dart';
import 'package:Laradrobe/providers/conversation_provider.dart';
import 'package:Laradrobe/providers/home_provider.dart';
import 'package:Laradrobe/providers/language_provider.dart';
import 'package:Laradrobe/providers/location_provider.dart';
import 'package:Laradrobe/providers/product_provider.dart';
import 'package:Laradrobe/providers/theme_provider.dart';
import 'package:Laradrobe/providers/wardrobe_provider.dart';
import 'package:Laradrobe/services/notification_service.dart';
import 'package:Laradrobe/theme/app_theme.dart';
import 'package:Laradrobe/utils/locale_string.dart';
import 'package:Laradrobe/utils/storage_helper.dart';
import 'package:in_app_update/in_app_update.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  

  // Initialize Firebase
  await Firebase.initializeApp(
    options: const FirebaseOptions(
      apiKey: "AIzaSyDMBHo79TcNQmSoMyRs68zuhkpt6ssUMHU",
      appId: "1:128217929900:android:4e773edb55d28aaaaae709",
      messagingSenderId: "128217929900",
      projectId: "laradrobe",
      storageBucket: "laradrobe.appspot.com",
    ),
  );

  // Load environment variables
  await dotenv.load(fileName: Env.fileName);

  // Initialize providers
  _initializeProviders();

  // Initialize notification service
  await NotificationService().initialize();

  runApp(const MyApp());
}

void _initializeProviders() {
  // Core providers - initialize immediately
  Get.put(ThemeProvider(), permanent: true);
  Get.put(LanguageProvider(), permanent: true);
  Get.put(AuthProvider(), permanent: true);

  // User related providers
  Get.lazyPut<LocationProvider>(() => LocationProvider(), fenix: true);

  // Content providers
  Get.lazyPut<HomeProvider>(() => HomeProvider(), fenix: true);
  Get.put(CategoryProvider(), permanent: true); // Changed from lazyPut with fenix to put with permanent
  Get.lazyPut<ProductProvider>(() => ProductProvider(), fenix: true);
  Get.lazyPut<WardrobeProvider>(() => WardrobeProvider(), fenix: true);
  Get.lazyPut<CartProvider>(() => CartProvider(), fenix: true);

  // Communication providers
  Get.lazyPut<ConversationProvider>(() => ConversationProvider(), fenix: true);

  // Navigation provider
  Get.put(BottomNavigationProvider(), permanent: true);
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late ThemeProvider themeProvider;
  late LanguageProvider languageProvider;
  late AuthProvider authProvider;

  @override
  void initState() {
    super.initState();
    _initializeProviderInstances();
    _checkInitialRoute();
  }

  void _initializeProviderInstances() {
    // Initialize provider instances
    try {
      themeProvider = Get.find<ThemeProvider>();
      languageProvider = Get.find<LanguageProvider>();
      authProvider = Get.find<AuthProvider>();
    } catch (e) {
      // If providers are not found, initialize them
      if (!Get.isRegistered<ThemeProvider>()) {
        Get.put(ThemeProvider());
        themeProvider = Get.find<ThemeProvider>();
      }

      if (!Get.isRegistered<LanguageProvider>()) {
        Get.put(LanguageProvider());
        languageProvider = Get.find<LanguageProvider>();
      }

      if (!Get.isRegistered<AuthProvider>()) {
        Get.put(AuthProvider());
        authProvider = Get.find<AuthProvider>();
      }
    }
  }

  Future<void> checkForUpdate() async {
    print('checking for Update');
    InAppUpdate.checkForUpdate().then((info) {
      if (info.updateAvailability == UpdateAvailability.updateAvailable) {
        print('update available');
        updateApp();
      }
    }).catchError((e) {
      print(e.toString());
    });
  }

  void updateApp() async {
    print('Updating');
    await InAppUpdate.startFlexibleUpdate();
    InAppUpdate.completeFlexibleUpdate().then((_) {}).catchError((e) {
      print(e.toString());
    });
  }

  Future<void> _checkInitialRoute() async {
    // We'll set the initial route directly in the GetMaterialApp
    // instead of using Get.offAllNamed which requires the GetMaterialApp to be built first
  }

  @override
  Widget build(BuildContext context) {
    // For development purposes, we'll just go to the main screen
    // In production, you would check onboarding status and authentication
    checkForUpdate();

    const initialRoute = Routes.main;

    return Obx(() => GetMaterialApp(
      title: 'Laradrobe',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.themeMode.value,
      locale: languageProvider.locale.value,
      fallbackLocale: const Locale('en', 'US'),
      translations: LocaleString(),
      initialRoute: initialRoute,
      getPages: Routes.pages,
      defaultTransition: Transition.cupertino,
      transitionDuration: const Duration(milliseconds: 300),
      builder: (context, child) {
        // Apply font scaling
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
    ));
  }
}
