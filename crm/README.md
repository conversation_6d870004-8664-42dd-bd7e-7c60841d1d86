# Laradrobe CRM System

A comprehensive CRM/Admin Panel for the Laradrobe clothing rental marketplace.

## 🏗️ Project Structure

```
crm/
├── backend/          # Laravel API
├── frontend/         # React Admin Panel
├── mobile/          # Flutter Mobile CRM
├── docker/          # Docker configuration
└── docs/           # Documentation
```

## 🚀 Technology Stack

### Backend (Laravel)
- **Framework**: <PERSON><PERSON> 10+
- **Database**: MySQL 8.0+
- **Authentication**: Laravel Sanctum
- **Roles & Permissions**: <PERSON><PERSON> Permission
- **File Storage**: Laravel Storage
- **Queue**: Redis/Database
- **Cache**: Redis

### Frontend (React)
- **Framework**: React 18+ with TypeScript
- **State Management**: Redux Toolkit + React Query
- **UI Library**: Material-UI (MUI)
- **Charts**: Chart.js
- **Forms**: React Hook Form
- **HTTP Client**: Axios

### Mobile (Flutter)
- **Framework**: Flutter 3.0+
- **State Management**: GetX
- **HTTP Client**: Dio
- **Local Storage**: Hive
- **Navigation**: GetX Navigation

## 🔧 Quick Setup

### Prerequisites
- PHP 8.1+
- Node.js 18+
- Flutter 3.0+
- MySQL 8.0+
- Redis (optional)

### Backend Setup
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate --seed
php artisan serve
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

### Mobile Setup
```bash
cd mobile
flutter pub get
flutter run
```

## 👥 Default Users & Roles

### Super Admin
- **Email**: <EMAIL>
- **Password**: password
- **Access**: Full system access

### Admin
- **Email**: <EMAIL>
- **Password**: password
- **Access**: General management

### Manager
- **Email**: <EMAIL>
- **Password**: password
- **Access**: Department management

### Support Agent
- **Email**: <EMAIL>
- **Password**: password
- **Access**: Customer support

## 🔐 Role-Based Permissions

### Permission Categories
- **Users Management**: view, create, edit, delete, suspend
- **Products Management**: view, create, edit, delete, approve
- **Orders Management**: view, create, edit, cancel, refund
- **Rentals Management**: view, approve, track, complete
- **Payments Management**: view, process, refund
- **Reports & Analytics**: view, export
- **Settings Management**: view, edit
- **Content Management**: view, create, edit, delete

## 📊 Key Features

### Dashboard
- Real-time analytics and metrics
- Revenue tracking and charts
- User activity monitoring
- Quick action buttons

### User Management
- Advanced search and filtering
- Bulk operations
- Activity history tracking
- Role assignment

### Product Management
- Product approval workflow
- Image gallery management
- Category organization
- Bulk operations

### Order & Rental Management
- Order lifecycle tracking
- Rental status updates
- Payment processing
- Return management

### Analytics & Reporting
- Interactive charts and graphs
- Custom date ranges
- Export functionality
- Real-time data updates

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/user` - Get authenticated user

### Users
- `GET /api/users` - List users with filters
- `POST /api/users` - Create new user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Products
- `GET /api/products` - List products
- `POST /api/products` - Create product
- `PUT /api/products/{id}` - Update product
- `POST /api/products/{id}/approve` - Approve product

### Orders
- `GET /api/orders` - List orders
- `GET /api/orders/{id}` - Get order details
- `PUT /api/orders/{id}/status` - Update order status

### Analytics
- `GET /api/analytics/dashboard` - Dashboard metrics
- `GET /api/analytics/revenue` - Revenue analytics
- `GET /api/analytics/users` - User analytics

## 🐳 Docker Support

```bash
# Start all services
docker-compose up -d

# Backend only
docker-compose up backend

# Frontend only
docker-compose up frontend
```

## 📱 Mobile CRM Features

### Field Agent Capabilities
- User lookup and verification
- Product inspection and approval
- Order processing
- Payment collection
- Offline data sync
- GPS tracking
- Camera integration

### Manager Features
- Team performance monitoring
- Territory management
- Real-time reporting
- Task assignment

## 🔒 Security Features

- JWT token authentication
- Rate limiting
- Input validation
- CORS configuration
- SQL injection prevention
- XSS protection
- File upload security

## 📈 Performance Optimization

- Database indexing
- API response caching
- Image optimization
- Lazy loading
- Query optimization
- Code splitting

## 🚀 Deployment

### Production Setup
1. Configure environment variables
2. Set up SSL certificates
3. Configure web server (Nginx/Apache)
4. Set up database backups
5. Configure monitoring

### Environment Variables
```env
# Backend (.env)
APP_NAME=Laradrobe CRM
APP_ENV=production
APP_URL=https://crm.laradrobe.com
DB_HOST=localhost
DB_DATABASE=laradrobe_crm
SANCTUM_STATEFUL_DOMAINS=crm.laradrobe.com

# Frontend (.env)
REACT_APP_API_URL=https://api.laradrobe.com
REACT_APP_APP_NAME=Laradrobe CRM
```

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Frontend Guide](docs/frontend.md)
- [Mobile App Guide](docs/mobile.md)
- [Deployment Guide](docs/deployment.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [docs/](docs/)
- Issues: GitHub Issues

---

**Laradrobe CRM** - Comprehensive clothing rental marketplace management system.
