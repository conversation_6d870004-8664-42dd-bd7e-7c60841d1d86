import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  People,
  Inventory,
  ShoppingCart,
  TrendingUp,
  Assignment,
  Payment,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '../../services/api';

interface DashboardStats {
  totalUsers: number;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  activeRentals: number;
  pendingPayments: number;
  userGrowth: number;
  revenueGrowth: number;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  growth?: number;
  loading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, growth, loading }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar sx={{ bgcolor: color, mr: 2 }}>
          {icon}
        </Avatar>
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
            {loading ? <LinearProgress /> : value}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {title}
          </Typography>
        </Box>
      </Box>
      {growth !== undefined && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Chip
            label={`${growth > 0 ? '+' : ''}${growth}%`}
            color={growth > 0 ? 'success' : growth < 0 ? 'error' : 'default'}
            size="small"
            icon={<TrendingUp />}
          />
          <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
            vs last month
          </Typography>
        </Box>
      )}
    </CardContent>
  </Card>
);

export const Dashboard: React.FC = () => {
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ['dashboard'],
    queryFn: async () => {
      const response = await apiService.getDashboard();
      return response.data.data as DashboardStats;
    },
  });

  const stats = [
    {
      title: 'Total Users',
      value: dashboardData?.totalUsers || 0,
      icon: <People />,
      color: '#1976d2',
      growth: dashboardData?.userGrowth,
    },
    {
      title: 'Total Products',
      value: dashboardData?.totalProducts || 0,
      icon: <Inventory />,
      color: '#388e3c',
    },
    {
      title: 'Total Orders',
      value: dashboardData?.totalOrders || 0,
      icon: <ShoppingCart />,
      color: '#f57c00',
    },
    {
      title: 'Total Revenue',
      value: `₹${dashboardData?.totalRevenue?.toLocaleString() || 0}`,
      icon: <Payment />,
      color: '#7b1fa2',
      growth: dashboardData?.revenueGrowth,
    },
    {
      title: 'Active Rentals',
      value: dashboardData?.activeRentals || 0,
      icon: <Assignment />,
      color: '#d32f2f',
    },
    {
      title: 'Pending Payments',
      value: dashboardData?.pendingPayments || 0,
      icon: <Payment />,
      color: '#ed6c02',
    },
  ];

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Welcome to Laradrobe CRM. Here's an overview of your business.
        </Typography>
      </Box>

      {/* Stats Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <StatCard
              title={stat.title}
              value={stat.value}
              icon={stat.icon}
              color={stat.color}
              growth={stat.growth}
              loading={isLoading}
            />
          </Grid>
        ))}
      </Grid>

      {/* Additional Dashboard Content */}
      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  Activity feed will be displayed here
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  Quick action buttons will be displayed here
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
