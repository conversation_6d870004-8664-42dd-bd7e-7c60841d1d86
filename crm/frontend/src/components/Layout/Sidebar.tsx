import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Divider,
  useTheme,
} from '@mui/material';
import {
  Dashboard,
  People,
  Inventory,
  ShoppingCart,
  Assignment,
  Payment,
  Analytics,
  Category,
  Settings,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
  width: number;
  isMobile: boolean;
}

interface MenuItem {
  text: string;
  icon: React.ReactElement;
  path: string;
  permission?: string;
}

const menuItems: MenuItem[] = [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
    permission: 'dashboard.view',
  },
  {
    text: 'Users',
    icon: <People />,
    path: '/users',
    permission: 'users.view',
  },
  {
    text: 'Products',
    icon: <Inventory />,
    path: '/products',
    permission: 'products.view',
  },
  {
    text: 'Orders',
    icon: <ShoppingCart />,
    path: '/orders',
    permission: 'orders.view',
  },
  {
    text: 'Rentals',
    icon: <Assignment />,
    path: '/rentals',
    permission: 'rentals.view',
  },
  {
    text: 'Payments',
    icon: <Payment />,
    path: '/payments',
    permission: 'payments.view',
  },
  {
    text: 'Analytics',
    icon: <Analytics />,
    path: '/analytics',
    permission: 'analytics.view',
  },
  {
    text: 'Categories',
    icon: <Category />,
    path: '/categories',
    permission: 'categories.view',
  },
  {
    text: 'Settings',
    icon: <Settings />,
    path: '/settings',
    permission: 'settings.view',
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ open, onClose, width, isMobile }) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  const hasPermission = (permission?: string) => {
    if (!permission || !user) return true;
    return user.permissions.includes(permission);
  };

  const filteredMenuItems = menuItems.filter(item => hasPermission(item.permission));

  const handleItemClick = (path: string) => {
    navigate(path);
    if (isMobile) {
      onClose();
    }
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          minHeight: 64,
        }}
      >
        <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
          Laradrobe
        </Typography>
      </Box>

      <Divider />

      {/* Navigation */}
      <List sx={{ flexGrow: 1, pt: 1 }}>
        {filteredMenuItems.map((item) => {
          const isActive = location.pathname === item.path;
          
          return (
            <ListItem key={item.text} disablePadding sx={{ px: 1 }}>
              <ListItemButton
                onClick={() => handleItemClick(item.path)}
                sx={{
                  borderRadius: 1,
                  mb: 0.5,
                  backgroundColor: isActive ? theme.palette.primary.main : 'transparent',
                  color: isActive ? 'white' : 'inherit',
                  '&:hover': {
                    backgroundColor: isActive 
                      ? theme.palette.primary.dark 
                      : theme.palette.action.hover,
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? 'white' : 'inherit',
                    minWidth: 40,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive ? 600 : 400,
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      {/* User info */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="body2" color="text.secondary">
          Logged in as
        </Typography>
        <Typography variant="subtitle2" noWrap>
          {user?.name}
        </Typography>
        <Typography variant="caption" color="text.secondary" noWrap>
          {user?.roles.join(', ')}
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'persistent'}
      open={open}
      onClose={onClose}
      sx={{
        width: width,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: width,
          boxSizing: 'border-box',
          borderRight: 1,
          borderColor: 'divider',
        },
      }}
      ModalProps={{
        keepMounted: true, // Better open performance on mobile.
      }}
    >
      {drawerContent}
    </Drawer>
  );
};
