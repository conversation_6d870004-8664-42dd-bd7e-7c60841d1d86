import axios, { AxiosInstance, AxiosResponse } from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(email: string, password: string): Promise<AxiosResponse> {
    return this.api.post('/auth/login', { email, password });
  }

  async logout(): Promise<AxiosResponse> {
    return this.api.post('/auth/logout');
  }

  async getUser(): Promise<AxiosResponse> {
    return this.api.get('/auth/user');
  }

  async changePassword(currentPassword: string, newPassword: string, newPasswordConfirmation: string): Promise<AxiosResponse> {
    return this.api.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
      new_password_confirmation: newPasswordConfirmation,
    });
  }

  // Dashboard
  async getDashboard(): Promise<AxiosResponse> {
    return this.api.get('/dashboard');
  }

  // Users
  async getUsers(params?: any): Promise<AxiosResponse> {
    return this.api.get('/users', { params });
  }

  async getUser(id: number): Promise<AxiosResponse> {
    return this.api.get(`/users/${id}`);
  }

  async createUser(data: any): Promise<AxiosResponse> {
    return this.api.post('/users', data);
  }

  async updateUser(id: number, data: any): Promise<AxiosResponse> {
    return this.api.put(`/users/${id}`, data);
  }

  async deleteUser(id: number): Promise<AxiosResponse> {
    return this.api.delete(`/users/${id}`);
  }

  async suspendUser(id: number): Promise<AxiosResponse> {
    return this.api.post(`/users/${id}/suspend`);
  }

  async activateUser(id: number): Promise<AxiosResponse> {
    return this.api.post(`/users/${id}/activate`);
  }

  // Products
  async getProducts(params?: any): Promise<AxiosResponse> {
    return this.api.get('/products', { params });
  }

  async getProduct(id: number): Promise<AxiosResponse> {
    return this.api.get(`/products/${id}`);
  }

  async createProduct(data: any): Promise<AxiosResponse> {
    return this.api.post('/products', data);
  }

  async updateProduct(id: number, data: any): Promise<AxiosResponse> {
    return this.api.put(`/products/${id}`, data);
  }

  async deleteProduct(id: number): Promise<AxiosResponse> {
    return this.api.delete(`/products/${id}`);
  }

  async approveProduct(id: number): Promise<AxiosResponse> {
    return this.api.post(`/products/${id}/approve`);
  }

  async rejectProduct(id: number, reason: string): Promise<AxiosResponse> {
    return this.api.post(`/products/${id}/reject`, { reason });
  }

  // Categories
  async getCategories(params?: any): Promise<AxiosResponse> {
    return this.api.get('/categories', { params });
  }

  async createCategory(data: any): Promise<AxiosResponse> {
    return this.api.post('/categories', data);
  }

  async updateCategory(id: number, data: any): Promise<AxiosResponse> {
    return this.api.put(`/categories/${id}`, data);
  }

  async deleteCategory(id: number): Promise<AxiosResponse> {
    return this.api.delete(`/categories/${id}`);
  }

  // Orders
  async getOrders(params?: any): Promise<AxiosResponse> {
    return this.api.get('/orders', { params });
  }

  async getOrder(id: number): Promise<AxiosResponse> {
    return this.api.get(`/orders/${id}`);
  }

  async updateOrderStatus(id: number, status: string): Promise<AxiosResponse> {
    return this.api.put(`/orders/${id}/status`, { status });
  }

  async cancelOrder(id: number, reason?: string): Promise<AxiosResponse> {
    return this.api.post(`/orders/${id}/cancel`, { reason });
  }

  async refundOrder(id: number, amount?: number): Promise<AxiosResponse> {
    return this.api.post(`/orders/${id}/refund`, { amount });
  }

  // Rentals
  async getRentals(params?: any): Promise<AxiosResponse> {
    return this.api.get('/rentals', { params });
  }

  async getRental(id: number): Promise<AxiosResponse> {
    return this.api.get(`/rentals/${id}`);
  }

  async completeRental(id: number, data: any): Promise<AxiosResponse> {
    return this.api.post(`/rentals/${id}/complete`, data);
  }

  // Payments
  async getPayments(params?: any): Promise<AxiosResponse> {
    return this.api.get('/payments', { params });
  }

  async processPayment(id: number, data: any): Promise<AxiosResponse> {
    return this.api.post(`/payments/${id}/process`, data);
  }

  async refundPayment(id: number, amount?: number): Promise<AxiosResponse> {
    return this.api.post(`/payments/${id}/refund`, { amount });
  }

  // Analytics
  async getAnalytics(type: string, params?: any): Promise<AxiosResponse> {
    return this.api.get(`/analytics/${type}`, { params });
  }

  // Utility
  async getRoles(): Promise<AxiosResponse> {
    return this.api.get('/utils/roles');
  }

  async getPermissions(): Promise<AxiosResponse> {
    return this.api.get('/utils/permissions');
  }
}

export const apiService = new ApiService();
