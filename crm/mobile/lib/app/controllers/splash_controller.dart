import 'package:get/get.dart';
import '../routes/app_routes.dart';
import '../services/storage_service.dart';
import '../services/api_service.dart';

class SplashController extends GetxController {
  final StorageService _storageService = StorageService.to;
  final ApiService _apiService = Get.find<ApiService>();

  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Show splash for at least 2 seconds
    await Future.delayed(Duration(seconds: 2));

    // Check if user is logged in
    final token = _storageService.getToken();
    
    if (token != null) {
      // Verify token with server
      try {
        final response = await _apiService.getUser();
        if (response.statusCode == 200) {
          // Token is valid, go to dashboard
          Get.offAllNamed(AppRoutes.DASHBOARD);
        } else {
          // Token is invalid, go to login
          await _storageService.clearAuthData();
          Get.offAllNamed(AppRoutes.LOGIN);
        }
      } catch (e) {
        // Network error or token invalid, go to login
        await _storageService.clearAuthData();
        Get.offAllNamed(AppRoutes.LOGIN);
      }
    } else {
      // No token, go to login
      Get.offAllNamed(AppRoutes.LOGIN);
    }
  }
}
