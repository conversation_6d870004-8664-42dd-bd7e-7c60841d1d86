import 'package:get/get.dart';
import 'app_routes.dart';
import '../views/splash/splash_view.dart';
import '../views/auth/login_view.dart';
import '../views/dashboard/dashboard_view.dart';
import '../views/users/users_view.dart';
import '../views/users/user_detail_view.dart';
import '../views/products/products_view.dart';
import '../views/products/product_detail_view.dart';
import '../views/orders/orders_view.dart';
import '../views/orders/order_detail_view.dart';
import '../views/rentals/rentals_view.dart';
import '../views/rentals/rental_detail_view.dart';
import '../views/payments/payments_view.dart';
import '../views/payments/payment_detail_view.dart';
import '../views/profile/profile_view.dart';
import '../views/settings/settings_view.dart';

import '../controllers/splash_controller.dart';
import '../controllers/auth_controller.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/users_controller.dart';
import '../controllers/products_controller.dart';
import '../controllers/orders_controller.dart';
import '../controllers/rentals_controller.dart';
import '../controllers/payments_controller.dart';

class AppPages {
  static final routes = [
    GetPage(
      name: AppRoutes.SPLASH,
      page: () => SplashView(),
      binding: BindingsBuilder(() {
        Get.put(SplashController());
      }),
    ),
    GetPage(
      name: AppRoutes.LOGIN,
      page: () => LoginView(),
      binding: BindingsBuilder(() {
        Get.put(AuthController());
      }),
    ),
    GetPage(
      name: AppRoutes.DASHBOARD,
      page: () => DashboardView(),
      binding: BindingsBuilder(() {
        Get.put(DashboardController());
      }),
    ),
    GetPage(
      name: AppRoutes.USERS,
      page: () => UsersView(),
      binding: BindingsBuilder(() {
        Get.put(UsersController());
      }),
    ),
    GetPage(
      name: AppRoutes.USER_DETAIL,
      page: () => UserDetailView(),
    ),
    GetPage(
      name: AppRoutes.PRODUCTS,
      page: () => ProductsView(),
      binding: BindingsBuilder(() {
        Get.put(ProductsController());
      }),
    ),
    GetPage(
      name: AppRoutes.PRODUCT_DETAIL,
      page: () => ProductDetailView(),
    ),
    GetPage(
      name: AppRoutes.ORDERS,
      page: () => OrdersView(),
      binding: BindingsBuilder(() {
        Get.put(OrdersController());
      }),
    ),
    GetPage(
      name: AppRoutes.ORDER_DETAIL,
      page: () => OrderDetailView(),
    ),
    GetPage(
      name: AppRoutes.RENTALS,
      page: () => RentalsView(),
      binding: BindingsBuilder(() {
        Get.put(RentalsController());
      }),
    ),
    GetPage(
      name: AppRoutes.RENTAL_DETAIL,
      page: () => RentalDetailView(),
    ),
    GetPage(
      name: AppRoutes.PAYMENTS,
      page: () => PaymentsView(),
      binding: BindingsBuilder(() {
        Get.put(PaymentsController());
      }),
    ),
    GetPage(
      name: AppRoutes.PAYMENT_DETAIL,
      page: () => PaymentDetailView(),
    ),
    GetPage(
      name: AppRoutes.PROFILE,
      page: () => ProfileView(),
    ),
    GetPage(
      name: AppRoutes.SETTINGS,
      page: () => SettingsView(),
    ),
  ];
}
