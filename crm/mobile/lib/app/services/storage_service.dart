import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

class StorageService extends GetxService {
  static StorageService get to => Get.find();
  
  late Box _box;

  Future<StorageService> init() async {
    _box = await Hive.openBox('laradrobe_crm');
    return this;
  }

  // Token management
  Future<void> setToken(String token) async {
    await _box.put('auth_token', token);
  }

  String? getToken() {
    return _box.get('auth_token');
  }

  Future<void> removeToken() async {
    await _box.delete('auth_token');
  }

  // User data
  Future<void> setUser(Map<String, dynamic> user) async {
    await _box.put('user_data', user);
  }

  Map<String, dynamic>? getUser() {
    return _box.get('user_data');
  }

  Future<void> removeUser() async {
    await _box.delete('user_data');
  }

  // App settings
  Future<void> setThemeMode(String mode) async {
    await _box.put('theme_mode', mode);
  }

  String getThemeMode() {
    return _box.get('theme_mode', defaultValue: 'system');
  }

  Future<void> setLanguage(String language) async {
    await _box.put('language', language);
  }

  String getLanguage() {
    return _box.get('language', defaultValue: 'en');
  }

  // Remember login
  Future<void> setRememberLogin(bool remember) async {
    await _box.put('remember_login', remember);
  }

  bool getRememberLogin() {
    return _box.get('remember_login', defaultValue: false);
  }

  // Last login credentials (only if remember is enabled)
  Future<void> setLastLoginEmail(String email) async {
    await _box.put('last_login_email', email);
  }

  String? getLastLoginEmail() {
    return _box.get('last_login_email');
  }

  // App version
  Future<void> setAppVersion(String version) async {
    await _box.put('app_version', version);
  }

  String? getAppVersion() {
    return _box.get('app_version');
  }

  // First time launch
  Future<void> setFirstTimeLaunch(bool isFirst) async {
    await _box.put('first_time_launch', isFirst);
  }

  bool isFirstTimeLaunch() {
    return _box.get('first_time_launch', defaultValue: true);
  }

  // Offline data cache
  Future<void> setCachedData(String key, dynamic data) async {
    await _box.put('cache_$key', data);
  }

  dynamic getCachedData(String key) {
    return _box.get('cache_$key');
  }

  Future<void> removeCachedData(String key) async {
    await _box.delete('cache_$key');
  }

  // Clear all data
  Future<void> clearAll() async {
    await _box.clear();
  }

  // Clear only auth data
  Future<void> clearAuthData() async {
    await removeToken();
    await removeUser();
  }
}
