import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'storage_service.dart';

class ApiService extends GetxService {
  late Dio _dio;
  final String baseUrl = 'http://localhost:8000/api'; // Change this to your API URL
  
  @override
  void onInit() {
    super.onInit();
    _initializeDio();
  }

  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(seconds: 30),
      receiveTimeout: Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        final token = await StorageService.to.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) {
        // Handle 401 errors (unauthorized)
        if (error.response?.statusCode == 401) {
          StorageService.to.removeToken();
          Get.offAllNamed('/login');
        }
        handler.next(error);
      },
    ));
  }

  // Auth endpoints
  Future<Response> login(String email, String password) async {
    return await _dio.post('/auth/login', data: {
      'email': email,
      'password': password,
    });
  }

  Future<Response> logout() async {
    return await _dio.post('/auth/logout');
  }

  Future<Response> getUser() async {
    return await _dio.get('/auth/user');
  }

  Future<Response> changePassword(String currentPassword, String newPassword, String confirmPassword) async {
    return await _dio.post('/auth/change-password', data: {
      'current_password': currentPassword,
      'new_password': newPassword,
      'new_password_confirmation': confirmPassword,
    });
  }

  // Dashboard
  Future<Response> getDashboard() async {
    return await _dio.get('/dashboard');
  }

  // Users
  Future<Response> getUsers({Map<String, dynamic>? params}) async {
    return await _dio.get('/users', queryParameters: params);
  }

  Future<Response> getUser(int id) async {
    return await _dio.get('/users/$id');
  }

  Future<Response> createUser(Map<String, dynamic> data) async {
    return await _dio.post('/users', data: data);
  }

  Future<Response> updateUser(int id, Map<String, dynamic> data) async {
    return await _dio.put('/users/$id', data: data);
  }

  Future<Response> deleteUser(int id) async {
    return await _dio.delete('/users/$id');
  }

  Future<Response> suspendUser(int id) async {
    return await _dio.post('/users/$id/suspend');
  }

  Future<Response> activateUser(int id) async {
    return await _dio.post('/users/$id/activate');
  }

  // Products
  Future<Response> getProducts({Map<String, dynamic>? params}) async {
    return await _dio.get('/products', queryParameters: params);
  }

  Future<Response> getProduct(int id) async {
    return await _dio.get('/products/$id');
  }

  Future<Response> approveProduct(int id) async {
    return await _dio.post('/products/$id/approve');
  }

  Future<Response> rejectProduct(int id, String reason) async {
    return await _dio.post('/products/$id/reject', data: {'reason': reason});
  }

  Future<Response> getPendingProducts() async {
    return await _dio.get('/products/pending/approval');
  }

  // Orders
  Future<Response> getOrders({Map<String, dynamic>? params}) async {
    return await _dio.get('/orders', queryParameters: params);
  }

  Future<Response> getOrder(int id) async {
    return await _dio.get('/orders/$id');
  }

  Future<Response> updateOrderStatus(int id, String status) async {
    return await _dio.put('/orders/$id/status', data: {'status': status});
  }

  Future<Response> cancelOrder(int id, {String? reason}) async {
    return await _dio.post('/orders/$id/cancel', data: {'reason': reason});
  }

  Future<Response> refundOrder(int id, {double? amount}) async {
    return await _dio.post('/orders/$id/refund', data: {'amount': amount});
  }

  // Rentals
  Future<Response> getRentals({Map<String, dynamic>? params}) async {
    return await _dio.get('/rentals', queryParameters: params);
  }

  Future<Response> getRental(int id) async {
    return await _dio.get('/rentals/$id');
  }

  Future<Response> completeRental(int id, Map<String, dynamic> data) async {
    return await _dio.post('/rentals/$id/complete', data: data);
  }

  Future<Response> updateRentalStatus(int id, String status) async {
    return await _dio.put('/rentals/$id/status', data: {'status': status});
  }

  Future<Response> getOverdueRentals() async {
    return await _dio.get('/rentals/overdue');
  }

  // Payments
  Future<Response> getPayments({Map<String, dynamic>? params}) async {
    return await _dio.get('/payments', queryParameters: params);
  }

  Future<Response> processPayment(int id, Map<String, dynamic> data) async {
    return await _dio.post('/payments/$id/process', data: data);
  }

  Future<Response> refundPayment(int id, {double? amount}) async {
    return await _dio.post('/payments/$id/refund', data: {'amount': amount});
  }

  // Analytics
  Future<Response> getAnalytics(String type, {Map<String, dynamic>? params}) async {
    return await _dio.get('/analytics/$type', queryParameters: params);
  }

  // Categories
  Future<Response> getCategories({Map<String, dynamic>? params}) async {
    return await _dio.get('/categories', queryParameters: params);
  }

  // Utility
  Future<Response> getRoles() async {
    return await _dio.get('/utils/roles');
  }

  Future<Response> getPermissions() async {
    return await _dio.get('/utils/permissions');
  }

  // File upload
  Future<Response> uploadFile(String endpoint, String filePath) async {
    FormData formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath),
    });
    return await _dio.post(endpoint, data: formData);
  }
}
