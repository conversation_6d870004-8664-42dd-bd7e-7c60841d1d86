# Laradrobe CRM Setup Guide

This guide will help you set up the complete Laradrobe CRM system with <PERSON><PERSON> backend, React frontend, and Flutter mobile app.

## 🏗️ System Architecture

```
Laradrobe CRM
├── Backend (Laravel API)
├── Frontend (React Admin Panel)
├── Mobile (Flutter CRM App)
└── Database (MySQL)
```

## 📋 Prerequisites

### Required Software
- **PHP 8.1+** with extensions: mbstring, xml, pdo_mysql, gd, curl
- **Composer** (PHP package manager)
- **Node.js 18+** and **npm**
- **Flutter 3.0+** and **Dart SDK**
- **MySQL 8.0+** or **MariaDB**
- **Git**

### Optional
- **Redis** (for caching and queues)
- **Docker** (for containerized deployment)

## 🚀 Quick Setup

### 1. Clone and Setup Backend (Laravel)

```bash
cd crm/backend

# Install dependencies
composer install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure database in .env file
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laradrobe_crm
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Run migrations and seeders
php artisan migrate --seed

# Create storage link
php artisan storage:link

# Start development server
php artisan serve
```

### 2. Setup Frontend (React)

```bash
cd crm/frontend

# Install dependencies
npm install

# Create environment file
echo "REACT_APP_API_URL=http://localhost:8000/api" > .env

# Start development server
npm start
```

### 3. Setup Mobile App (Flutter)

```bash
cd crm/mobile

# Get dependencies
flutter pub get

# Run on device/emulator
flutter run
```

## 🔧 Detailed Configuration

### Backend Configuration

#### Environment Variables (.env)
```env
APP_NAME="Laradrobe CRM"
APP_ENV=local
APP_KEY=base64:your_generated_key
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laradrobe_crm
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

SANCTUM_STATEFUL_DOMAINS=localhost:3000,127.0.0.1:3000
```

#### Database Setup
```sql
CREATE DATABASE laradrobe_crm;
```

#### Run Migrations and Seeders
```bash
php artisan migrate:fresh --seed
```

This will create:
- Users table with role-based permissions
- Products, Categories, Orders, Rentals, Payments tables
- Sample admin users with different roles
- Sample categories and data

### Frontend Configuration

#### Environment Variables (.env)
```env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_APP_NAME=Laradrobe CRM
```

#### Available Scripts
```bash
npm start          # Start development server
npm run build      # Build for production
npm test           # Run tests
npm run eject      # Eject from Create React App
```

### Mobile App Configuration

#### Update API URL
Edit `crm/mobile/lib/app/services/api_service.dart`:
```dart
final String baseUrl = 'http://your-api-url.com/api';
```

For local development:
- Android Emulator: `http://********:8000/api`
- iOS Simulator: `http://localhost:8000/api`
- Physical Device: `http://your-computer-ip:8000/api`

## 👥 Default User Accounts

| Role | Email | Password | Permissions |
|------|-------|----------|-------------|
| Super Admin | <EMAIL> | password | Full access |
| Admin | <EMAIL> | password | General management |
| Manager | <EMAIL> | password | Department management |
| Support Agent | <EMAIL> | password | Customer support |
| Field Agent | <EMAIL> | password | Mobile operations |
| Accountant | <EMAIL> | password | Financial access |
| Content Manager | <EMAIL> | password | Content management |

## 🔐 Role-Based Permissions

### Permission Categories
- **Users Management**: view, create, edit, delete, suspend
- **Products Management**: view, create, edit, delete, approve
- **Orders Management**: view, create, edit, cancel, refund
- **Rentals Management**: view, approve, track, complete
- **Payments Management**: view, process, refund
- **Reports & Analytics**: view, export
- **Settings Management**: view, edit
- **Content Management**: view, create, edit, delete

## 📱 Mobile App Features

### Field Agent Capabilities
- User lookup and verification
- Product inspection and approval
- Order processing
- Payment collection
- Offline data sync
- GPS tracking
- Camera integration

### Manager Features
- Team performance monitoring
- Territory management
- Real-time reporting
- Task assignment

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/user` - Get authenticated user

### Users
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Products
- `GET /api/products` - List products
- `POST /api/products/{id}/approve` - Approve product
- `POST /api/products/{id}/reject` - Reject product

### Orders & Rentals
- `GET /api/orders` - List orders
- `GET /api/rentals` - List rentals
- `POST /api/rentals/{id}/complete` - Complete rental

### Analytics
- `GET /api/analytics/dashboard` - Dashboard metrics
- `GET /api/analytics/revenue` - Revenue analytics

## 🐳 Docker Deployment

### Using Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Individual Services
```bash
# Backend only
docker-compose up backend

# Frontend only
docker-compose up frontend
```

## 🔧 Troubleshooting

### Common Issues

#### Backend Issues
1. **Migration errors**: Ensure database exists and credentials are correct
2. **Permission errors**: Run `chmod -R 775 storage bootstrap/cache`
3. **Key not set**: Run `php artisan key:generate`

#### Frontend Issues
1. **API connection**: Check REACT_APP_API_URL in .env
2. **CORS errors**: Ensure backend CORS is configured for frontend URL
3. **Build errors**: Clear node_modules and reinstall

#### Mobile Issues
1. **API connection**: Update baseUrl in api_service.dart
2. **Build errors**: Run `flutter clean && flutter pub get`
3. **Permission errors**: Add required permissions in AndroidManifest.xml

### Performance Optimization

#### Backend
- Enable Redis for caching
- Configure queue workers
- Optimize database queries
- Enable compression

#### Frontend
- Build for production: `npm run build`
- Enable gzip compression
- Implement code splitting
- Optimize images

#### Mobile
- Build release APK: `flutter build apk --release`
- Enable ProGuard for Android
- Optimize images and assets

## 📚 Additional Resources

- [Laravel Documentation](https://laravel.com/docs)
- [React Documentation](https://reactjs.org/docs)
- [Flutter Documentation](https://flutter.dev/docs)
- [Material-UI Documentation](https://mui.com/)
- [GetX Documentation](https://pub.dev/packages/get)

## 🆘 Support

For issues and questions:
- Check the troubleshooting section above
- Review the API documentation
- Check application logs
- Contact the development team

---

**Laradrobe CRM** - Complete clothing rental marketplace management solution
