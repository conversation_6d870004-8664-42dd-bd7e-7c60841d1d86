name: <PERSON><PERSON><PERSON>
description: "A new Flutter project."
publish_to: 'none'
version: 1.3.1+52

environment:
  sdk: '>=3.3.3 <4.0.0'

dependencies:
  android_play_install_referrer: ^0.4.0
  app_links: ^6.3.3
  cached_network_image: ^3.3.1
  clippy_flutter: ^2.0.0-nullsafety.1
  dotted_border: ^2.1.0
  firebase_analytics: ^11.3.3
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  flip_card: ^0.7.0
  flutter:
    sdk: flutter
  flutter_chat_ui: ^1.6.14
  flutter_dotenv: ^5.1.0
  flutter_facebook_auth: ^7.0.1
  flutter_file_downloader: 2.1.0
    # git:
    #   url: https://github.com/abdallah-odeh/flutter_file_downloader
    #   ref: master
  flutter_launcher_icons: ^0.13.1
  flutter_local_notifications: ^17.2.3
  flutter_native_splash: ^2.4.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_typeahead: ^5.2.0
  full_screen_image: ^2.0.0
  geocoding: ^3.0.0
  geolocator: ^12.0.0
  get: ^4.6.6
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.1
  http: ^1.2.1
  image_cropper: ^8.0.2
  image_picker: ^1.1.1
  in_app_update: ^4.2.3
  intl: ^0.20.2
  introduction_screen: ^3.1.14
  photo_view: ^0.15.0
  shared_preferences: ^2.2.3
  shimmer: ^3.0.0
  # twitter_login: ^4.4.2
  # uni_links: ^0.5.1
  uuid: ^4.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter_icons:
  android: true
  image_path: "assets/images/logo.jpeg"

flutter:
  uses-material-design: true

  assets:
   - .env.prod
   - .env.dev
   - assets/images/logo.png
   - assets/images/

flutter_native_splash:
  color: "#EDD3D6"
  image: "assets/images/logo.png"
